package tools

import (
	"context"
	"fmt"
	"time"

	"sentioxyz/sentio/chain/sui/sql"
	"sentioxyz/sentio/common/clickhouse/builder"
	"sentioxyz/sentio/common/log"

	"github.com/ClickHouse/clickhouse-go/v2"
)

type CoinPriceCreator struct {
	ctx                   context.Context
	clickhouseDSN         string
	clickhouseClusterArgv string
	priceDBDSN            string
	tableName             string
	metadataTableName     string
	priceTableName        string

	clickhouseConn clickhouse.Conn
	clickhouseOpt  *clickhouse.Options
	priceDBConn    clickhouse.Conn
	priceOpt       *clickhouse.Options
	scanInterval   time.Duration
}

func NewCoinPriceCreator(ctx context.Context,
	clickhouseDSN, priceDBDSN, tableName, metadataTableName, priceTableName string,
	scanInterval time.Duration) SuiSyncer {
	creator := &CoinPriceCreator{
		ctx:               ctx,
		clickhouseDSN:     clickhouseDSN,
		priceDBDSN:        priceDBDSN,
		tableName:         tableName,
		metadataTableName: metadataTableName,
		priceTableName:    priceTableName,
		scanInterval:      scanInterval,
	}

	connect := func(dsn string) (clickhouse.Conn, *clickhouse.Options) {
		opt, err := clickhouse.ParseDSN(dsn)
		if err != nil {
			panic(err)
		}
		conn, err := clickhouse.Open(opt)
		if err != nil {
			panic(err)
		}
		return conn, opt
	}
	creator.clickhouseConn, creator.clickhouseOpt = connect(clickhouseDSN)
	creator.priceDBConn, creator.priceOpt = connect(priceDBDSN)
	creator.clickhouseClusterArgv = creator.clusterArgv()
	return creator
}

func (c *CoinPriceCreator) clusterArgv() string {
	ctx, logger := log.FromContext(c.ctx)
	row := c.clickhouseConn.QueryRow(ctx, "SELECT cluster FROM system.clusters WHERE host_address='127.0.0.1'"+
		" AND cluster not like 'all-%' LIMIT 1")
	if row.Err() != nil {
		logger.Errorf("get cluster failed: %v", row.Err())
		return ""
	}
	var cluster string
	err := row.Scan(&cluster)
	if err != nil {
		logger.Errorf("scan cluster failed: %v", err)
		return ""
	}
	if cluster == "" {
		return ""
	} else {
		return " ON CLUSTER '" + cluster + "'"
	}
}

func (c *CoinPriceCreator) clickhouseExec(ctx context.Context, f string, v ...any) error {
	ctx = clickhouse.Context(ctx, clickhouse.WithSettings(clickhouse.Settings{
		"allow_experimental_lightweight_delete": "true",
	}))
	sql := fmt.Sprintf(f, v...)
	startTime := time.Now()
	err := c.clickhouseConn.Exec(ctx, sql)
	_, logger := log.FromContext(ctx)
	logger = logger.With("module", "sui.coin_price")
	if err == nil {
		logger.Debugf("ClickHouse exec: %s (%v)", sql, time.Since(startTime))
	} else {
		logger.Warnfe(err, "ClickHouse exec failed (%v): %s", err, sql)
	}
	return err
}

func (c *CoinPriceCreator) createView() error {
	remoteTable := func(conn clickhouse.Conn, opt *clickhouse.Options, tableName string) string {
		return fmt.Sprintf("remote('%s','%s','%s','%s','%s')",
			opt.Addr[0], opt.Auth.Database, tableName, opt.Auth.Username, opt.Auth.Password)
	}
	stmt := builder.FormatSQLTemplate(sql.SuiCoinPriceCreateStmt, map[string]any{
		"name":                               c.tableName,
		"cluster":                            c.clickhouseClusterArgv,
		"price_database_token_price_name":    remoteTable(c.priceDBConn, c.priceOpt, c.priceTableName),
		"price_database_token_metadata_name": remoteTable(c.priceDBConn, c.priceOpt, c.metadataTableName),
	})
	_, logger := log.FromContext(c.ctx)
	logger = logger.With("module", "sui.coin_price")
	logger.Infof("creating view %q: %s", c.tableName, stmt)
	return c.clickhouseExec(c.ctx, stmt)
}

func (c *CoinPriceCreator) runOnce() error {
	_, logger := log.FromContext(c.ctx)
	logger = logger.With("module", "sui.coin_price")
	rows, err := c.clickhouseConn.Query(c.ctx, "SHOW TABLES WHERE name = ?", c.tableName)
	if err != nil {
		return fmt.Errorf("clickhouse show tables name is %q failed: %w", c.tableName, err)
	}
	defer rows.Close()
	has := rows.Next()
	if rows.Err() != nil {
		return fmt.Errorf("clickhouse scan rows failed: %w", rows.Err())
	}
	if !has {
		logger.Infof("table %q not found, will try to create it", c.tableName)
		return c.createView()
	} else {
		logger.Infof("table %q already exists", c.tableName)
	}
	return nil
}

func (c *CoinPriceCreator) Run() error {
	ctx, logger := log.FromContext(c.ctx)
	logger = logger.With("module", "sui.coin_price")
	if err := c.runOnce(); err != nil {
		logger.Errorf("failed to run once: %v", err)
		return err
	}
	ticker := time.NewTicker(c.scanInterval)
	for {
		select {
		case <-ctx.Done():
			return nil
		case <-ticker.C:
			if err := c.runOnce(); err != nil {
				logger.Errorf("failed to run once: %v", err)
				continue
			}
		}
	}
}

package models

import (
	"encoding/json"
	"sentioxyz/sentio/service/billing/protos"

	"google.golang.org/protobuf/types/known/timestamppb"

	"google.golang.org/protobuf/types/known/structpb"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type PaymentIntent struct {
	gorm.Model
	ID             string `gorm:"primaryKey"`
	InvoiceID      string
	Invoice        *Invoice
	PaymentGateway PaymentGateway
	Data           datatypes.JSON
	Sent           bool
}

func (i PaymentIntent) ToPB(withData bool) *protos.PaymentIntent {
	intent := &protos.PaymentIntent{
		Id:        i.ID,
		InvoiceId: i.InvoiceID,
		Sent:      i.Sent,
		UpdatedAt: timestamppb.New(i.UpdatedAt),
		CreatedAt: timestamppb.New(i.CreatedAt),
	}
	gateway := protos.PaymentGateway_value[string(i.PaymentGateway)]
	intent.PaymentGateway = protos.PaymentGateway(gateway)

	if withData {
		data := map[string]interface{}{}
		_ = json.Unmarshal(i.Data, &data)
		intent.Data, _ = structpb.NewStruct(data)
	}

	if i.Invoice != nil {
		intent.Invoice = i.Invoice.ToPB()
	}

	return intent
}

type PaymentGateway string

const (
	Stripe         PaymentGateway = "STRIPE"
	Coinbase       PaymentGateway = "COIN_BASE"
	RequestFinance PaymentGateway = "REQUEST_FINANCE"
)

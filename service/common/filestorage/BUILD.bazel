load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "filestorage",
    srcs = [
        "filestorage.go",
        "gcsstorage.go",
        "s3storage.go",
        "storage_settings.go",
    ],
    importpath = "sentioxyz/sentio/service/common/filestorage",
    visibility = ["//visibility:public"],
    deps = [
        "//common/log",
        "@com_github_minio_minio_go_v7//:minio-go",
        "@com_github_minio_minio_go_v7//pkg/credentials",
        "@com_google_cloud_go_storage//:storage",
    ],
)

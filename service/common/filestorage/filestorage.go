package filestorage

import (
	"context"
	"fmt"
	"strings"
	"time"
)

type FileStorageEngine interface {
	PreSignedPutUrl(ctx context.Context, bucket, object, contentType string, expireDuration time.Duration) (string, error)
	PreSignedGetUrl(ctx context.Context, bucket, object string, expireDuration time.Duration) (string, error)
	MoveFile(ctx context.Context, srcBucket, srcObject, destBucket, destObject string) error
	GetUrl(ctx context.Context, bucket, object string) string
	Delete(ctx context.Context, bucket, object string) error
}

type FileStorageSystem struct {
	Settings *StorageSettings
}

func (fs *FileStorageSystem) NewFile(ctx context.Context, object string) (*FileObject, error) {
	engine, err := fs.createDefaultStorage()
	if err != nil {
		return nil, err
	}
	return &FileObject{
		object: object,
		engine: engine,
		bucket: fs.Settings.Bucket,
	}, nil
}

func (fs *FileStorageSystem) createDefaultStorage() (FileStorageEngine, error) {
	s := fs.Settings
	if s.S3Endpoint != "" {
		return NewS3FileStorage(s.S3Endpoint, s.S3AccessKeyID, s.S3SecretKey)
	} else if s.GcsServiceAccount != "" {
		return NewGCSFileStorage(s.GcsServiceAccount), nil
	} else {
		return nil, fmt.Errorf("no GcsServiceAccount or S3Endpoint provided")
	}
}

func (fs *FileStorageSystem) Get(engine FileStorageEngine, bucket, object string) *FileObject {
	return &FileObject{
		engine: engine,
		bucket: bucket,
		object: object,
	}
}

func (fs *FileStorageSystem) GetFromUrl(ctx context.Context, url string) (*FileObject, error) {
	protocol, rest, found := strings.Cut(url, "://")
	if !found {
		return nil, fmt.Errorf("invalid storage URL: %s", url)
	}
	bucket, object, _ := strings.Cut(rest, "/")
	s := fs.Settings
	if protocol == "gcs" {
		if s.GcsServiceAccount == "" {
			return nil, fmt.Errorf("GCS service account is not configured")
		}

		return &FileObject{
			bucket: bucket,
			object: object,
			engine: NewGCSFileStorage(s.GcsServiceAccount),
		}, nil
	} else if protocol == "s3" {
		if s.S3Endpoint == "" {
			return nil, fmt.Errorf("S3 storage is not  configured")
		}
		storage, err := NewS3FileStorage(s.S3Endpoint, s.S3AccessKeyID, s.S3SecretKey)
		return &FileObject{
			bucket: bucket,
			object: object,
			engine: storage,
		}, err
	} else {
		return nil, fmt.Errorf("unsupported storage protocol: %s", protocol)
	}
}

type FileObject struct {
	engine FileStorageEngine `json:"-"`
	object string
	bucket string
}

func (f *FileObject) PreSignedUploadUrl(ctx context.Context, contentType string, expireDuration time.Duration) (string, error) {
	return f.engine.PreSignedPutUrl(ctx, f.bucket, f.object, contentType, expireDuration)
}

func (f *FileObject) PreSignedDownloadUrl(ctx context.Context, expireDuration time.Duration) (string, error) {
	return f.engine.PreSignedGetUrl(ctx, f.bucket, f.object, expireDuration)
}

func (f *FileObject) MoveTo(ctx context.Context, destBucket, destObject string) error {
	return f.engine.MoveFile(ctx, f.bucket, f.object, destBucket, destObject)
}

func (f *FileObject) GetUrl(ctx context.Context) string {
	return f.engine.GetUrl(ctx, f.bucket, f.object)
}

func (f *FileObject) Delete(ctx context.Context) error {
	return f.engine.Delete(ctx, f.bucket, f.object)
}

func (f *FileObject) GetObject() string {
	return f.object
}

func (f *FileObject) GetBucket() string {
	return f.bucket
}

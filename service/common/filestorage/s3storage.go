package filestorage

import (
	"context"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"time"
)

const S3URLPrefix = "s3://"

type S3FileStorage struct {
	Client *minio.Client
}

func NewS3FileStorage(endpoint, accessKeyID, secretAccessKey string) (*S3FileStorage, error) {
	minioClient, err := minio.New(endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(accessKeyID, secretAccessKey, ""),
		Secure: true,
	})
	if err != nil {
		return nil, err
	}
	return &S3FileStorage{
		Client: minioClient,
	}, nil
}

func (s *S3FileStorage) PreSignedPutUrl(ctx context.Context, bucket, fileUrl, contentType string, expireDuration time.Duration) (string, error) {
	reqParams := make(map[string]string)
	if contentType != "" {
		reqParams["Content-Type"] = contentType
	}
	u, err := s.Client.PresignedPutObject(ctx, bucket, fileUrl, 5*time.Minute)
	return u.String(), err
}

func (s *S3FileStorage) PreSignedGetUrl(ctx context.Context, bucket, fileUrl string, expireDuration time.Duration) (string, error) {
	u, err := s.Client.PresignedGetObject(ctx, bucket, fileUrl, 5*time.Minute, nil)
	return u.String(), err
}

func (s *S3FileStorage) CopyFile(ctx context.Context, srcBucket, srcFileUrl, destBucket, destFileUrl string) error {
	if _, err := s.Client.CopyObject(ctx, minio.CopyDestOptions{
		Bucket: srcBucket,
		Object: destFileUrl,
	}, minio.CopySrcOptions{
		Bucket: destBucket,
		Object: srcFileUrl,
	}); err != nil {
		return err
	}
	return nil
}

func (s *S3FileStorage) GetUrl(ctx context.Context, bucket, fileUrl string) string {
	return S3URLPrefix + bucket + "/" + fileUrl
}

func (s *S3FileStorage) Delete(ctx context.Context, bucket, fileUrl string) error {
	return s.Client.RemoveObject(ctx, bucket, fileUrl, minio.RemoveObjectOptions{})
}

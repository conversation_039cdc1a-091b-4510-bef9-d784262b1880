package processor

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"time"

	"sentioxyz/sentio/common/gonanoid"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/processor"
	"sentioxyz/sentio/service/common/auth"
	modelscommon "sentioxyz/sentio/service/common/models"
	"sentioxyz/sentio/service/processor/models"
	"sentioxyz/sentio/service/processor/protos"

	"cloud.google.com/go/storage"
	"github.com/redis/go-redis/v9"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
)

const GcsURLPrefix = "gcs://"

var ErrNotGcsURL = errors.New("not gcs url")
var ErrInvalidGcsURL = errors.New("invalid gcs url")
var ErrBucketMismatch = errors.New("bucket mismatch")

func (s *Service) CheckKey(
	ctx context.Context,
	_ *emptypb.Empty,
) (*protos.CheckKeyResponse, error) {
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}
	if !strings.HasPrefix(identity.Sub, modelscommon.APIKeyPrefix) {
		return nil, fmt.Errorf("no API key found")
	}
	return &protos.CheckKeyResponse{Username: identity.User.Username}, nil
}

func (s *Service) InitUpload(
	ctx context.Context,
	req *protos.InitUploadRequest,
) (*protos.InitUploadResponse, error) {
	logger := log.WithContext(ctx)
	project, _, err := s.authProject(ctx, req.ProjectSlug)
	if err != nil {
		logger.Errorf("auth project failed, err=%v", err)
		return nil, err
	}
	var warning string
	if project.Type == modelscommon.ProjectTypeSubgraph {
		if req.Sequence != 2 {
			return nil, fmt.Errorf("invalid sequence: %d", req.Sequence)
		}
	} else {
		if req.Sequence > 1 {
			return nil, fmt.Errorf("invalid sequence: %d", req.Sequence)
		}

		clientVersion := req.SdkVersion
		serverVersion := processor.HostProcessorVersion()
		err = processor.VersionCheck(serverVersion, clientVersion)
		if err != nil {
			warning = err.Error()
		}
	}

	key := fmt.Sprintf("processor-upload-%s-%d", project.ID, req.Sequence)
	fileID, err := s.redisClient.Get(ctx, key).Result()
	if err != nil {
		if err != redis.Nil {
			logger.Errorf("redis get failed, err=%v", err)
			return nil, err
		}
		// generate one if not exists in redis
		fileID, err = s.generateFileID()
		if err != nil {
			logger.Errorf("generate file id failed, err=%v", err)
			return nil, err
		}
		ret := s.redisClient.Set(ctx, key, fileID, 10*time.Minute)
		if err := ret.Err(); err != nil {
			logger.Errorf("redis set failed, err=%v", err)
			return nil, err
		}
	}

	contentType := "application/octet-stream"
	if req.ContentType != "" {
		contentType = req.ContentType
	}
	file, err := s.fileStorageSystem.NewUploadFile(ctx, fileID, contentType)
	if err != nil {
		return nil, err
	}
	url, err := file.PreSignedUploadUrl(ctx, 5*time.Minute)
	if err != nil {
		logger.Errorf("sign url failed, err=%v", err)
		return nil, err
	}

	var replacingVersion int32
	replacingProcessor, _ := s.repository.FindReplacingProcessor(s.db, project)
	if replacingProcessor != nil {
		replacingVersion = replacingProcessor.Version
	}
	return &protos.InitUploadResponse{
		Url:              url,
		Warning:          warning,
		ReplacingVersion: replacingVersion,
		MultiVersion:     project.MultiVersion,
		ProjectId:        project.ID,
	}, nil
}

func (s *Service) FinishUpload(
	ctx context.Context,
	req *protos.FinishUploadRequest,
) (*protos.FinishUploadResponse, error) {
	logger := log.WithContext(ctx)
	project, identity, err := s.authProject(ctx, req.ProjectSlug)
	if err != nil {
		logger.Errorf("auth project failed, err=%v", err)
		return nil, err
	}
	defaultStorage, err := s.storageSettings.CreateDefaultStorage()
	if err != nil {
		return nil, err
	}
	renameFile := func(seq int32) (string, error) {
		key := fmt.Sprintf("processor-upload-%s-%d", project.ID, seq)
		fileID, err := s.redisClient.Get(ctx, key).Result()
		if err != nil {
			logger.Errorf("redis get failed, key=%s, err=%v", key, err)
			return fileID, err
		}
		file := s.fileStorageSystem.GetUploadFileByID(defaultStorage, fileID)
		err = defaultStorage.MoveFile(ctx, s.storageSettings.Bucket, s.storageSettings.UploadPrefix+"/"+fileID, s.storageSettings.Bucket, fileID)
		return fileID, err
	}

	if project.Type == modelscommon.ProjectTypeSubgraph {
		// for subgraph project, will only exist the file with sequence 2
		if req.Sequence != 2 {
			return nil, fmt.Errorf("invalid sequence: %d", req.Sequence)
		}
		var p *models.Processor
		if req.ContinueFrom > 0 {
			log.Infof("specified continue from for project %s, will update properties of processor %d",
				req.ProjectSlug, req.ContinueFrom)
			var ps []*models.Processor
			ps, err = s.repository.GetProcessorsByProjectAndVersion(ctx, project.ID, req.ContinueFrom)
			if err != nil {
				return nil, fmt.Errorf("find processor with version %d failed: %w", req.ContinueFrom, err)
			}
			if len(ps) == 0 {
				return nil, fmt.Errorf("find processor with version %d failed: not found", req.ContinueFrom)
			}
			if len(ps) > 1 {
				logger.Warnf("%d processors found for project %s with version %d", len(ps), project.ID, req.ContinueFrom)
			}
			p = ps[0]
		} else {
			p, err = s.repository.FindLastProcessor(ctx, project.ID)
			if err != nil {
				return nil, fmt.Errorf("find last processor failed: %w", err)
			}
		}

		fileID, err := renameFile(2)
		if err != nil {
			return nil, err
		}
		p.ZipURL = defaultStorage.GetUrl(ctx, s.storageSettings.Bucket, fileID)
		p.CliVersion = req.CliVersion
		p.SdkVersion = req.SdkVersion
		p.Warnings = req.Warnings
		if err := s.repository.DB.Save(p).Error; err != nil {
			err = fmt.Errorf("update zipURL for processor %s/%s/%d/%s failed: %w",
				project.GetOwnerName(), project.Slug, p.Version, p.ID, err)
			logger.Errore(err)
			return nil, err
		}
		log.Infof("updated properties for processor %s with version %d of subgraph project %s",
			p.ID, p.Version, req.ProjectSlug)
		return &protos.FinishUploadResponse{
			ProjectFullSlug: project.GetOwnerName() + "/" + project.Slug,
			ProcessorId:     p.ID,
			Version:         p.Version,
		}, nil
	}

	if req.Sequence > 1 {
		return nil, fmt.Errorf("invalid sequence: %d", req.Sequence)
	}

	var fileIDs [2]string
	// move processor file to permanent storage
	for seq := int32(0); seq <= req.Sequence; seq++ {
		fileIDs[seq], err = renameFile(seq)
		if err != nil {
			return nil, err
		}
	}
	var zipURL string

	if fileIDs[1] != "" {
		zipURL = defaultStorage.GetUrl(ctx, s.storageSettings.Bucket, fileIDs[1])
	}

	if req.ContinueFrom > 0 {
		log.Infof("specified continue from for project %s, will try to upgrade processor %d",
			req.ProjectSlug, req.ContinueFrom)
	}
	p, err := s.createOrUpdateProcessor(
		ctx,
		identity,
		project,
		req.ContinueFrom,
		models.SentioProcessorProperties{
			CliVersion:       req.CliVersion,
			SdkVersion:       req.SdkVersion,
			CodeURL:          defaultStorage.GetUrl(ctx, s.storageSettings.Bucket, fileIDs[0]),
			CodeHash:         req.Sha256,
			CommitSha:        req.CommitSha,
			GitURL:           req.GitUrl,
			ZipURL:           zipURL,
			Debug:            req.Debug,
			NetworkOverrides: models.BuildNetworkOverrides(req.NetworkOverrides),
		},
		models.SubgraphProcessorProperties{},
	)
	if err != nil {
		return nil, err
	}

	return &protos.FinishUploadResponse{
		ProjectFullSlug: project.GetOwnerName() + "/" + project.Slug,
		ProcessorId:     p.ID,
		Version:         p.Version,
	}, nil
}

func (s *Service) downloadFromStorage(
	ctx context.Context,
	processorID string,
	field string,
	expireTime ...time.Duration,
) (string, error) {
	var p *models.Processor
	err := s.db.WithContext(ctx).
		Where("id = ?", processorID).First(&p).Error
	if err != nil {
		return "", err
	}
	fieldValue := reflect.ValueOf(*p).FieldByName(field).String()
	obj, err := s.storageSettings.ParseObjectFromUrl(fieldValue)
	if err != nil {
		// compat with old local files
		if errors.Is(err, ErrNotGcsURL) {
			return fieldValue, nil
		}
		return "", err
	}

	expire := 5 * time.Minute
	if len(expireTime) > 0 && expireTime[0] > 0 {
		expire = expireTime[0]
	}

	url, err := obj.Storage.PreSignedGetUrl(ctx, obj.Bucket, obj.Object, expire)
	return url, err
}

func (s *Service) DownloadProcessor(
	ctx context.Context,
	req *protos.DownloadProcessorRequest,
) (*protos.DownloadProcessorResponse, error) {
	var p *models.Processor
	err := s.db.WithContext(ctx).
		Where("id = ?", req.ProcessorId).First(&p).Error
	if err != nil {
		return nil, err
	}

	obj, err := s.storageSettings.ParseObjectFromUrl(p.CodeURL)
	if err != nil {
		// compat with old local files
		if errors.Is(err, ErrNotGcsURL) {
			return &protos.DownloadProcessorResponse{
				Url: p.CodeURL,
			}, nil
		}
		return nil, err
	}

	url, err := obj.Storage.PreSignedGetUrl(ctx, obj.Bucket, obj.Object, 5*time.Minute)
	if err != nil {
		return nil, err
	}
	return &protos.DownloadProcessorResponse{
		Url: url,
	}, nil
}

func (s *Service) GetProcessorCode(
	ctx context.Context,
	req *protos.ProcessorIdRequest,
) (*protos.GetProcessorCodeResponse, error) {
	p, err := s.requireProcessorAccess(ctx, req.Id, auth.READ)
	if err != nil {
		return nil, err
	}
	p, err = s.repository.ResolveReferenceProcessor(ctx, p)
	if err != nil {
		return nil, err
	}

	codeURL, err := s.downloadFromStorage(ctx, p.ID, "CodeURL", 1*time.Hour)
	if err != nil {
		log.Fatale(err, "download CodeURL failed")
	}
	zipURL, err := s.downloadFromStorage(ctx, p.ID, "ZipURL", 1*time.Hour)
	if err != nil {
		log.Fatale(err, "download ZipURL failed")
	}
	return &protos.GetProcessorCodeResponse{
		CodeUrl: codeURL,
		ZipUrl:  zipURL,
	}, nil
}

func (s *Service) buildGCSDownloadURL(rawURL string) (string, error) {
	obj, err := s.storageSettings.ParseObjectFromUrl(rawURL)
	if err != nil {
		// compat with old local files
		if errors.Is(err, ErrNotGcsURL) {
			return rawURL, nil
		}
		return "", err
	}
	return obj.Storage.PreSignedGetUrl(context.Background(), obj.Bucket, obj.Object, 5*time.Minute)
}

func (s *Service) GetProcessorHistoryCode(
	ctx context.Context,
	req *protos.GetProcessorCodeRequest,
) (*protos.GetProcessorCodeResponse, error) {
	if _, err := s.requireProcessorAccess(ctx, req.Id, auth.WRITE); err != nil {
		return nil, err
	}
	var p models.ProcessorUpgradeHistory
	result := s.db.WithContext(ctx).Where("id = ? AND processor_id = ?", req.HistoryId, req.Id).First(&p)
	if result.Error != nil {
		return nil, result.Error
	}
	codeURL, err := s.buildGCSDownloadURL(p.CodeURL)
	if err != nil {
		return nil, fmt.Errorf("download CodeURL failed: %w", err)
	}
	zipURL, err := s.buildGCSDownloadURL(p.ZipURL)
	if err != nil {
		return nil, fmt.Errorf("download ZipURL failed: %w", err)
	}
	return &protos.GetProcessorCodeResponse{
		CodeUrl: codeURL,
		ZipUrl:  zipURL,
	}, nil
}

func (s *Service) authProject(
	ctx context.Context,
	projectSlug string,
) (*modelscommon.Project, *modelscommon.Identity, error) {
	if projectSlug == "" {
		return nil, nil, status.Errorf(codes.InvalidArgument, "project not specified")
	}
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, nil, err
	}

	db := s.db.WithContext(ctx)
	user := modelscommon.User{}
	result := db.First(&user, &modelscommon.User{ID: identity.UserID})
	if result.Error != nil {
		return nil, nil, result.Error
	}

	var owner string
	if strings.Contains(projectSlug, "/") {
		parts := strings.Split(projectSlug, "/")
		owner = parts[0]
		projectSlug = parts[1]
	} else {
		owner = user.Username
	}

	var project *modelscommon.Project
	project, err = s.repository.GetProjectBySlug(ctx, owner, projectSlug)
	if err != nil || project == nil {
		return nil, nil, status.Errorf(codes.NotFound, "project %s/%s not found", owner, projectSlug)
	}

	access, err := s.auth.CheckProjectAccess(ctx, identity, project, auth.WRITE)
	if err != nil {
		return nil, nil, err
	}
	if !access {
		return nil, nil, status.Errorf(codes.Unauthenticated, "not authorized")
	}
	return project, identity, nil
}

func (s *Service) deleteProcessorGcs(ctx context.Context, processor *models.Processor) error {
	obj, err := s.storageSettings.ParseObjectFromUrl(processor.CodeURL)
	if err != nil {
		if errors.Is(err, ErrNotGcsURL) {
			// compat with old local files
			return nil
		}
		return err
	}
	err = obj.Delete(ctx)
	if err != nil && !errors.Is(err, storage.ErrObjectNotExist) {
		return err
	}
	return nil
}

func (s *Service) generateFileID() (string, error) {
	id, err := gonanoid.GenerateID()
	if err != nil {
		return "", err
	}
	return s.storageSettings.Prefix + "/" + id, nil
}

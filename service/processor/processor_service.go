package processor

import (
	"compress/gzip"
	"context"
	"fmt"
	"io"
	"net/http"
	"sentioxyz/sentio/service/common/filestorage"
	"strconv"
	"strings"
	"time"

	"sentioxyz/sentio/service/analytic/sqllib/mapper"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/prometheus/common/model"

	"sentioxyz/sentio/common/event"

	"cloud.google.com/go/storage"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/notification"
	"sentioxyz/sentio/common/timescale"
	"sentioxyz/sentio/common/utils"
	"sentioxyz/sentio/driver/entity/cleaner"
	api "sentioxyz/sentio/k8s/api/v1"
	"sentioxyz/sentio/k8s/client"
	"sentioxyz/sentio/k8s/controllers"
	"sentioxyz/sentio/service/common/auth"
	commonerrors "sentioxyz/sentio/service/common/errors"
	modelscommon "sentioxyz/sentio/service/common/models"
	commonprotos "sentioxyz/sentio/service/common/protos"
	"sentioxyz/sentio/service/common/rpc"
	"sentioxyz/sentio/service/processor/models"
	"sentioxyz/sentio/service/processor/protos"
	"sentioxyz/sentio/service/processor/repository"
	protoswebhook "sentioxyz/sentio/service/webhook/protos"

	serviceredis "sentioxyz/sentio/service/common/redis"

	promapi "github.com/prometheus/client_golang/api"
	v1 "github.com/prometheus/client_golang/api/prometheus/v1"
)

type Service struct {
	protos.UnimplementedProcessorServiceServer
	db                       *gorm.DB
	repository               *repository.Repository
	auth                     auth.AuthManager
	runLocal                 bool
	timescaleMultiClient     timescale.MultiClient
	entityCleaner            cleaner.Cleaner
	k8sClient                client.MultiClient
	driverJobWatcher         map[int]*controllers.Reconciler
	webhookService           string
	redisClient              *redis.Client
	notificationClient       *notification.Client
	clickhouseMultiSharding  event.MultiSharding
	processorClusterSharding ShardingController
	promClient               map[int]promapi.Client
	logCh                    clickhouse.Conn
	fileStorageSystem        *filestorage.FileStorageSystem
}

func NewService(db *gorm.DB,
	authConfig *auth.AuthConfig,
	runLocal bool,
	timescaleConfigPath string,
	entityCleaner cleaner.Cleaner,
	k8sClient client.MultiClient,
	driverJobWatcher map[int]*controllers.Reconciler,
	webhookService string,
	clickhouseMultiSharding event.MultiSharding,
	processorClusterShardingConfig string,
	promClient map[int]promapi.Client,
	logCh clickhouse.Conn,
	storageSettings filestorage.StorageSettings,
) *Service {
	redisClient := serviceredis.NewClientWithDefaultOptions()
	log.Infof("processor cluster sharding config: %s", processorClusterShardingConfig)

	s := &Service{
		db:                       db,
		auth:                     auth.NewAuthManager(authConfig, db),
		repository:               repository.NewRepository(db),
		runLocal:                 runLocal,
		timescaleMultiClient:     timescale.MustNewMultiClient(timescaleConfigPath),
		entityCleaner:            entityCleaner,
		k8sClient:                k8sClient,
		driverJobWatcher:         driverJobWatcher,
		webhookService:           webhookService,
		redisClient:              redisClient,
		notificationClient:       notification.NewClient(db),
		clickhouseMultiSharding:  clickhouseMultiSharding,
		processorClusterSharding: newShardingController(processorClusterShardingConfig),
		promClient:               promClient,
		logCh:                    logCh,
		fileStorageSystem:        &filestorage.FileStorageSystem{Settings: &storageSettings},
	}
	return s
}

func (s *Service) GetProcessor(
	ctx context.Context,
	req *protos.GetProcessorRequest,
) (*protos.GetProcessorResponse, error) {
	if req.ProcessorId == "" {
		return nil, errors.Errorf("empty processor id")
	}
	var processor models.Processor
	query := s.db.WithContext(ctx).Preload("ChainStates")
	query = query.Where(&models.Processor{ID: req.GetProcessorId()})
	if result := query.Find(&processor); result.Error == nil {
		var err error
		var pb *protos.Processor
		referencedProcessor, err := s.repository.ResolveReferenceProcessor(ctx, &processor)
		if err != nil {
			return nil, err
		}
		if pb, err = referencedProcessor.ToPB(referencedProcessor); err != nil {
			return nil, err
		}

		response := protos.GetProcessorResponse{
			Processor: pb,
		}
		return &response, nil
	} else {
		return nil, result.Error
	}
}

func (s *Service) GetProcessorUpgradeHistories(
	ctx context.Context,
	req *protos.GetProcessorUpgradeHistoryRequest,
) (*protos.GetProcessorUpgradeHistoryResponse, error) {
	var processor models.Processor
	if result := s.db.Preload(clause.Associations).First(&processor, "id = ?", req.ProcessorId); result.Error != nil {
		return nil, result.Error
	}
	if _, _, err := s.auth.RequiredLoginForProject(ctx, processor.ProjectID, auth.READ); err != nil {
		return nil, err
	}
	histories, err := s.repository.ListProcessorUpgradeHistory(ctx, req.ProcessorId)
	if err != nil {
		return nil, err
	}
	resp := protos.GetProcessorUpgradeHistoryResponse{
		Histories: make([]*protos.ProcessorUpgradeHistory, len(histories)),
	}
	for i, h := range histories {
		if resp.Histories[i], err = h.ToPB(i, &processor); err != nil {
			return nil, err
		}
	}
	return &resp, nil
}

func (s *Service) GetProcessorWithProject(
	ctx context.Context,
	req *protos.GetProcessorRequest,
) (*protos.GetProcessorWithProjectResponse, error) {
	if req.ProcessorId == "" {
		return nil, errors.Errorf("empty processor id")
	}
	db := s.db.WithContext(ctx)
	// get processor and project from db
	var processor models.Processor
	var err error
	query := db.Preload("ChainStates").Where(&models.Processor{ID: req.GetProcessorId()})
	if result := query.Find(&processor); result.Error != nil {
		return nil, fmt.Errorf("get processor from db failed: %w", result.Error)
	}
	projectID := processor.ProjectID

	referencedProcessor, err := s.repository.ResolveReferenceProcessor(ctx, &processor)
	if err != nil {
		return nil, err
	}

	processor.Project = &modelscommon.Project{ID: projectID}
	if result := db.First(processor.Project); result.Error != nil {
		return nil, fmt.Errorf("get project from db failed: %w", result.Error)
	}
	// convert to pb objects
	var response protos.GetProcessorWithProjectResponse

	if response.Processor, err = processor.ToPB(referencedProcessor); err != nil {
		return nil, fmt.Errorf("convert processor model to pb object failed: %w", err)
	}

	response.Project = processor.Project.ToPB()
	return &response, nil
}

// GetProjectVariables is internal interface, should return secret value
func (s *Service) GetProjectVariables(
	ctx context.Context,
	req *protos.GetProjectVariablesRequest,
) (*commonprotos.ProjectVariables, error) {
	vars, err := s.repository.GetProjectVariables(ctx, req.ProjectId)
	if err != nil {
		return nil, err
	}
	result := &commonprotos.ProjectVariables{
		Variables: make([]*commonprotos.ProjectVariables_Variable, len(vars)),
		ProjectId: req.ProjectId,
	}
	for i, v := range vars {
		result.Variables[i] = v.ToPB(false)
	}
	return result, nil
}

func (s *Service) GetProcessors(
	ctx context.Context,
	req *protos.GetProcessorsRequest,
) (*protos.GetProcessorsResponse, error) {
	if req.ProjectId == "" {
		return nil, errors.Errorf("empty project id")
	}
	var processors []models.Processor
	query := s.db.WithContext(ctx).Preload("ChainStates")
	query = query.Where(&models.Processor{ProjectID: req.GetProjectId()})
	response := protos.GetProcessorsResponse{}
	if result := query.Find(&processors); result.Error == nil {

		for _, processor := range processors {
			referenceProcessor, err := s.repository.ResolveReferenceProcessor(ctx, &processor)
			if err != nil {
				return nil, err
			}
			var pb *protos.Processor

			if pb, err = processor.ToPB(referenceProcessor); err != nil {
				return nil, err
			}

			response.Processors = append(response.Processors, pb)
		}
		return &response, nil
	} else {
		return nil, result.Error
	}
}

func (s *Service) RecordNotificationInternal(
	ctx context.Context,
	req *protos.RecordNotificationRequest,
) (*emptypb.Empty, error) {
	attributes := make([]string, 0, len(req.Attributes)*2)
	for k, v := range req.Attributes {
		attributes = append(attributes, k, v)
	}
	return &emptypb.Empty{}, s.notificationClient.RecordProjectNotification(
		notification.Source(req.Source),
		notification.Level(req.Level),
		req.ProjectId,
		commonprotos.NotificationType_PROCESSOR_GENERAL,
		req.Msg,
		attributes...,
	)
}

func (s *Service) UpdateChainProcessorStatus(
	ctx context.Context,
	req *protos.UpdateChainProcessorStatusRequest,
) (*protos.UpdateChainProcessorStatusResponse, error) {
	var processor models.Processor
	if result := s.db.First(
		&processor, "id = ?", req.Id,
	); result.Error == nil {
		err := s.db.Transaction(
			func(tx *gorm.DB) error {
				var stateModel models.ChainState
				if err := stateModel.FromPB(req.ChainState, req.Id); err != nil {
					return err
				}
				tx.Save(&stateModel)
				return nil
			},
		)

		if err != nil {
			return nil, err
		}
		return &protos.UpdateChainProcessorStatusResponse{}, nil
	} else {
		return nil, result.Error
	}
}

func (s *Service) GetProcessorStatusV2(
	ctx context.Context,
	req *protos.GetProcessorStatusRequestV2,
) (*protos.GetProcessorStatusResponse, error) {
	_, err := s.auth.LoginOrAnonymous(ctx)
	if err != nil {
		return nil, err
	}
	projectID, err := s.repository.GetProjectIDBySlug(ctx, req.GetProjectOwner(), req.GetProjectSlug())
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, status.Error(codes.NotFound, "project not found")
	}
	if err != nil {
		return nil, err
	}
	return s.getProcessorStatus(ctx, projectID, "", req.GetVersion())
}

func (s *Service) GetProcessorStatus(
	ctx context.Context,
	req *protos.GetProcessorStatusRequest,
) (*protos.GetProcessorStatusResponse, error) {
	_, err := s.auth.LoginOrAnonymous(ctx)
	if err != nil {
		return nil, err
	}
	return s.GetProcessorStatusInternal(ctx, req)
}

func (s *Service) GetProcessorStatusInternal(
	ctx context.Context,
	req *protos.GetProcessorStatusRequest,
) (*protos.GetProcessorStatusResponse, error) {
	if projectID := req.GetProjectId(); projectID != "" {
		// check if the project exists
		project := &modelscommon.Project{}
		if err := s.db.First(project, "id = ?", projectID).Error; err != nil {
			return nil, err
		}
	}
	return s.getProcessorStatus(ctx, req.GetProjectId(), req.GetId(), protos.GetProcessorStatusRequestV2_ALL)
}

func (s *Service) getProcessorStatus(
	ctx context.Context,
	projectID string,
	processorID string,
	versionSelector protos.GetProcessorStatusRequestV2_VersionSelector,
) (*protos.GetProcessorStatusResponse, error) {
	var err error
	var processors []*models.Processor
	where := &models.Processor{
		ID:        processorID,
		ProjectID: projectID,
	}
	switch versionSelector {
	case protos.GetProcessorStatusRequestV2_ACTIVE:
		where.VersionState = int32(protos.ProcessorVersionState_ACTIVE)
	case protos.GetProcessorStatusRequestV2_PENDING:
		where.VersionState = int32(protos.ProcessorVersionState_PENDING)
	}
	result := s.db.WithContext(ctx).
		Preload("ChainStates", func(db *gorm.DB) *gorm.DB {
			return db.Omit("Templates", "IndexerState", "Trackers", "MeterState")
		}).
		Preload("User").
		Where(where).
		Find(&processors)
	if result.Error != nil {
		return nil, result.Error
	}

	response := protos.GetProcessorStatusResponse{
		Processors: make([]*protos.GetProcessorStatusResponse_ProcessorEx, len(processors)),
	}

	for i, processor := range processors {
		originProcessor := processor
		if len(originProcessor.ReferenceProjectID) > 0 && originProcessor.VersionState != int32(protos.ProcessorVersionState_OBSOLETE) {
			rp, err := s.repository.ResolveReferenceProcessor(ctx, originProcessor)
			if err != nil {
				return nil, err
			}
			if rp == nil {
				continue
			}
			var ps []*models.Processor
			s.db.WithContext(ctx).Preload("ChainStates", func(db *gorm.DB) *gorm.DB {
				return db.Omit("Templates", "IndexerState", "Trackers", "MeterState")
			}).Preload("User").Where(&models.Processor{ID: rp.ID}).Find(&ps)
			if len(ps) > 0 {
				processor = ps[0]
			}
		}

		var metaChain *models.ChainState
		var chainStates []*models.ChainState
		for _, state := range processor.ChainStates {
			if state.ChainID == "meta" {
				metaChain = state
				continue
			}
			chainStates = append(chainStates, state)
		}
		processorStatus := &protos.GetProcessorStatusResponse_ProcessorStatus{}
		var states []*protos.ChainState
		if metaChain == nil || (metaChain.State != int32(protos.ChainState_Status_ERROR) &&
			metaChain.State != int32(protos.ChainState_Status_UNKNOWN)) {
			if metaChain == nil {
				processorStatus.State = protos.GetProcessorStatusResponse_ProcessorStatus_STARTING
				if processor.VersionState == int32(protos.ProcessorVersionState_OBSOLETE) {
					processorStatus.State = protos.GetProcessorStatusResponse_ProcessorStatus_UNKNOWN
				}
			} else {
				processorStatus.State = protos.GetProcessorStatusResponse_ProcessorStatus_PROCESSING
			}
			if states, err = utils.MapSlice(chainStates,
				func(t *models.ChainState) (*protos.ChainState, error) {
					return t.ToPB()
				}); err != nil {
				return nil, err
			}
			// If processing, check if any chain is in error state.
			for _, state := range states {
				if state.Status.State == protos.ChainState_Status_ERROR {
					// 1 means processor error. Always show processor error.
					if processorStatus.State == protos.GetProcessorStatusResponse_ProcessorStatus_PROCESSING ||
						state.Status.ErrorRecord.NamespaceCode == 1 {
						processorStatus.State = protos.GetProcessorStatusResponse_ProcessorStatus_ERROR
						processorStatus.ErrorRecord = state.Status.ErrorRecord
					}
				}
			}
		} else {
			processorStatus.State = protos.GetProcessorStatusResponse_ProcessorStatus_ERROR
			processorStatus.ErrorRecord = metaChain.ErrorRecord.ToPB()
			if states, err = utils.MapSlice(chainStates,
				func(t *models.ChainState) (*protos.ChainState, error) {
					t.State = int32(protos.ChainState_Status_ERROR)
					t.ErrorRecord = metaChain.ErrorRecord
					return t.ToPB()
				}); err != nil {
				return nil, err
			}
		}
		var uploadedBy *commonprotos.UserInfo
		if processor.User != nil {
			uploadedBy = processor.User.ToUserInfo()
		}
		// fix processor status by driver job status
		if processorStatus.State != protos.GetProcessorStatusResponse_ProcessorStatus_UNKNOWN &&
			(processorStatus.State != protos.GetProcessorStatusResponse_ProcessorStatus_ERROR ||
				processorStatus.ErrorRecord.GetNamespace() != commonerrors.PROCESSOR) {
			if dr, has := s.driverJobWatcher[int(processor.K8sClusterID)].GetByProcessorID(processor.ID); has &&
				dr.Status.Phase == api.PhaseRunning {
				processorStatus.State = protos.GetProcessorStatusResponse_ProcessorStatus_PROCESSING
			} else {
				processorStatus.State = protos.GetProcessorStatusResponse_ProcessorStatus_STARTING
			}
		}
		// If processor is in error state, mark every chain in error.
		if processorStatus.State == protos.GetProcessorStatusResponse_ProcessorStatus_ERROR {
			for _, state := range states {
				if state.Status.State == protos.ChainState_Status_ERROR {
					continue
				}
				state.Status.State = protos.ChainState_Status_ERROR
			}
		}
		// If processor is starting, every chain state is queuing.
		if processorStatus.State == protos.GetProcessorStatusResponse_ProcessorStatus_STARTING {
			for _, state := range states {
				if state.Status.State == protos.ChainState_Status_ERROR {
					continue
				}
				state.Status.State = protos.ChainState_Status_QUEUING
			}
		}

		networkOverrides := make([]*protos.NetworkOverride, len(processor.NetworkOverrides))
		for i, no := range processor.NetworkOverrides {
			networkOverrides[i] = &protos.NetworkOverride{Chain: no.Chain, Host: no.Host}
		}
		response.Processors[i] = &protos.GetProcessorStatusResponse_ProcessorEx{
			States:           states,
			ProcessorId:      originProcessor.ID,
			CodeHash:         processor.CodeHash,
			CommitSha:        processor.CommitSha,
			UploadedBy:       uploadedBy,
			UploadedAt:       timestamppb.New(processor.UploadedAt),
			ProcessorStatus:  processorStatus,
			Version:          originProcessor.Version,
			CliVersion:       processor.CliVersion,
			SdkVersion:       processor.SdkVersion,
			GitUrl:           processor.GitURL,
			VersionState:     protos.ProcessorVersionState(processor.VersionState),
			VersionLabel:     processor.VersionLabel,
			IpfsHash:         processor.IpfsHash,
			DebugFork:        processor.DebugFork,
			Warnings:         processor.Warnings,
			Pause:            processor.Pause,
			NetworkOverrides: models.ParseNetworkOverrides(processor.NetworkOverrides),
		}
		if originProcessor.ID != processor.ID {
			response.Processors[i].ReferenceProjectId = processor.ProjectID
		}
	}

	return &response, nil
}

func (s *Service) RemoveProcessor(
	ctx context.Context,
	req *protos.ProcessorIdRequest,
) (*protos.RemoveProcessorResponse, error) {
	processor, err := s.requireProcessorAccess(ctx, req.Id, auth.WRITE)
	if err != nil {
		return nil, err
	}
	db := s.db.WithContext(ctx)
	err = db.Transaction(func(tx *gorm.DB) error {
		return s.removeProcessor(ctx, tx, req.Id)
	})
	if err != nil {
		return nil, err
	}
	_ = s.notificationClient.RecordProjectNotification(notification.Processor,
		notification.Info,
		processor.ProjectID,
		commonprotos.NotificationType_PROCESSOR_STOPPED,
		"Processor has been stopped.",
		"version", fmt.Sprintf("%d", processor.Version),
	)
	if pb, err := processor.ToPB(nil); err != nil {
		return nil, err
	} else {
		resp := &protos.RemoveProcessorResponse{
			Deleted: pb,
		}
		return resp, nil
	}
}

func (s *Service) StartProcessorInternal(ctx context.Context, req *protos.ProcessorIdRequest) (*emptypb.Empty, error) {
	db := s.db.WithContext(ctx)
	var processor models.Processor
	if err := db.First(&processor, "id = ?", req.Id).Error; err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, s.startJob(ctx, &processor)
}

func (s *Service) RemoveProcessorInternal(
	ctx context.Context,
	req *protos.ProcessorIdRequest,
) (*emptypb.Empty, error) {
	db := s.db.WithContext(ctx)
	err := db.Transaction(func(tx *gorm.DB) error {
		return s.removeProcessor(ctx, tx, req.Id)
	})
	return &emptypb.Empty{}, err
}

const (
	asyncDropMetricsTimeout       = time.Second * 10
	asyncEraseEntitySchemaTimeout = time.Second * 60
)

func (s *Service) removeProcessor(
	ctx context.Context,
	tx *gorm.DB,
	id string,
) error {
	var processor models.Processor
	if result := tx.Unscoped().First(&processor, "id = ?", id); result.Error != nil {
		return result.Error
	}
	var project modelscommon.Project
	if result := tx.Unscoped().First(&project, "id = ?", processor.ProjectID); result.Error != nil {
		return result.Error
	}
	processor.Project = &project

	var err error
	logger := log.WithContext(ctx)
	if !s.runLocal {
		err = s.DeleteJob(ctx, &processor)
		if err != nil {
			logger.Errore(err)
			return err
		}
	}

	if err = s.repository.RemoveProcessor(ctx, tx, &processor); err != nil {
		return err
	}
	if s.webhookService != "" {
		// delete subscription if exists
		conn, err := rpc.Dial(s.webhookService)
		if err != nil {
			return err
		}
		defer conn.Close()
		c := protoswebhook.NewWebhookServiceClient(conn)
		_, err = c.DeleteSubscription(ctx, &protoswebhook.DeleteSubscriptionRequest{
			ProcessorId: id,
		})
		if err != nil {
			log.Errore(err, "failed to delete webhook subscription")
		}
	}

	switch processor.Project.Type {
	case modelscommon.ProjectTypeSubgraph:
		return nil
	default:
		// drop all metrics
		go func() {
			// Because ctx comes from the user's request, and the deletion here is asynchronous,
			// so ctx may have been canceled when this coroutine is executed, so ctx cannot be used directly here
			rmCtx, cancel := context.WithTimeout(context.Background(), asyncDropMetricsTimeout)
			defer cancel()
			timescaleClient, err := s.timescaleMultiClient.GetClient(rmCtx, processor.TimescaleShardingIndex)
			if err != nil {
				log.Errore(err, "failed to get timescale client")
			}
			err = timescaleClient.DropAllMetrics(rmCtx, processor.ID, -1, true)
			if err != nil {
				log.Errore(err, "failed to drop metrics")
			}
		}()

		// not doing gcs cleaning here
		// return s.deleteProcessorGcs(ctx, &processor)
		return nil
	}
}

// MaxPendingVersion how many versions of metrics data to keep
const MaxPendingVersion = 0
const MaxObsoleteVersion = 10

func (s *Service) GetProjectVersions(
	ctx context.Context,
	req *protos.GetProjectVersionsRequest,
) (*protos.GetProjectVersionsResponse, error) {
	/*	_, err := s.auth.RequiredLoginForProjectID(ctx, req.ProjectId, auth.READ)
		if err != nil {
			return nil, err
		}*/
	var err error
	var processors []*models.Processor
	if err = s.db.WithContext(ctx).Where("project_id =? and version_state <> ?",
		req.ProjectId, int32(protos.ProcessorVersionState_OBSOLETE)).Find(&processors).Error; err != nil {
		return nil, err
	}
	var versions []*protos.GetProjectVersionsResponse_Version
	for _, p := range processors {
		versions = append(versions, &protos.GetProjectVersionsResponse_Version{
			Version:     p.Version,
			ProcessorId: p.ID,
			State:       protos.ProcessorVersionState(p.VersionState),
		})
	}
	return &protos.GetProjectVersionsResponse{
		Versions: versions,
	}, nil
}

func (s *Service) RestartProcessor(ctx context.Context, req *protos.GetProcessorRequest) (*emptypb.Empty, error) {
	processor, err := s.requireProcessorAccess(ctx, req.ProcessorId, auth.WRITE)
	if err != nil {
		return nil, err
	}
	db := s.db.WithContext(ctx)
	if !s.runLocal {
		if err := s.k8sClient[int(processor.K8sClusterID)].RestartProcessor(ctx, processor.ID); err != nil {
			log.Infofe(err, "restart processor %q.%q.%d failed", processor.ProjectID, processor.ID, processor.Version)
		}
	}

	timescaleClient, err := s.timescaleMultiClient.GetClient(ctx, processor.TimescaleShardingIndex)
	if err != nil {
		return nil, err
	}
	err = timescaleClient.DropAllMetrics(ctx, processor.ID, -1, true)
	if err != nil {
		return nil, err
	}

	if err = db.Unscoped().Delete(&models.ChainState{}, "processor_id = ?", processor.ID).Error; err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *Service) RerunProcessorAsNewVersion(ctx context.Context, req *protos.RerunProcessorRequest) (*emptypb.Empty, error) {
	var processor models.Processor
	if result := s.db.Preload(clause.Associations).First(&processor, "id = ?", req.ProcessorId); result.Error != nil {
		return nil, result.Error
	}
	var err error
	var identity *modelscommon.Identity
	if identity, err = s.auth.RequiredAdminLogin(ctx); err != nil {
		identity, err = s.auth.RequiredLoginForProjectID(ctx, processor.ProjectID, auth.WRITE)
		if err != nil {
			return nil, err
		}
	}
	if req.SdkVersion != "" {
		processor.SdkVersion = req.SdkVersion
	}
	_, logger := log.FromContextWithTrace(ctx,
		"processorID", req.ProcessorId,
		"processorVersion", processor.Version,
		"projectID", processor.ProjectID,
		"projectOwnerType", processor.Project.OwnerType,
		"projectFullName", processor.Project.FullName(),
		"userID", identity.UserID,
		"sdkVersion", processor.SdkVersion)
	// create or update processor
	var newProcessor *models.Processor
	newProcessor, err = s.createOrUpdateProcessor(
		ctx,
		&modelscommon.Identity{UserID: *processor.UserID},
		processor.Project,
		0,
		processor.SentioProcessorProperties,
		processor.SubgraphProcessorProperties,
	)
	if err != nil {
		logger.Warne(err, "rerun processor failed")
		return nil, err
	}
	logger.Infow("rerun processor succeed",
		"newProcessorID", newProcessor.ID,
		"newProcessorVersion", newProcessor.Version)
	return &emptypb.Empty{}, nil
}

func (s *Service) ResumeWaitingProcessorsInternal(
	ctx context.Context,
	req *protos.ResumeWaitingProcessorsRequest,
) (*emptypb.Empty, error) {
	if s.runLocal {
		return &emptypb.Empty{}, nil
	}
	err := s.k8sClient.RestartWaitingProcessors(ctx, req.GetOwnerId(), req.GetOwnerType())
	if err != nil {
		err = fmt.Errorf(
			"restart waiting processors owned by %q/%q failed: %w",
			req.GetOwnerType(),
			req.GetOwnerId(),
			err,
		)
	}
	return &emptypb.Empty{}, err
}

func (s *Service) SetVersionActive(ctx context.Context, req *protos.GetProcessorRequest) (*emptypb.Empty, error) {
	processor, err := s.requireProcessorAccess(ctx, req.ProcessorId, auth.WRITE)
	if err != nil {
		return nil, err
	}
	db := s.db.WithContext(ctx)
	processor.VersionState = int32(protos.ProcessorVersionState_ACTIVE)
	err = db.Transaction(func(tx *gorm.DB) error {
		if err := s.activateProcessor(ctx, tx, processor, false); err != nil {
			return err
		}
		return db.Save(&processor).Error
	})
	if err != nil {
		return nil, err
	}
	_ = s.notificationClient.RecordProjectNotification(notification.Processor, notification.Info,
		processor.ProjectID,
		commonprotos.NotificationType_PROCESSOR_ACTIVATED,
		"Processor version activated.",
		"version", fmt.Sprintf("%d", processor.Version),
	)
	return &emptypb.Empty{}, nil
}

func (s *Service) activateProcessor(ctx context.Context, tx *gorm.DB, processor *models.Processor, upgrade bool) error {
	var err error
	if processor.VersionState == int32(protos.ProcessorVersionState_PENDING) {
		// stop other pending versions
		var pendingProcessors []models.Processor
		if err = tx.Where("project_id = ? and version_state = ? and id <> ?",
			processor.ProjectID, protos.ProcessorVersionState_PENDING, processor.ID).Find(&pendingProcessors).Error; err != nil {
			return err
		}
		for _, p := range pendingProcessors {
			err = s.obsoleteProcessor(ctx, tx, p)
			if err != nil {
				return err
			}
		}
	} else if processor.VersionState == int32(protos.ProcessorVersionState_ACTIVE) {
		// stop other active versions
		var activeProcessors []models.Processor
		if err = tx.Where("project_id = ? and version_state = ? and id <> ?",
			processor.ProjectID, protos.ProcessorVersionState_ACTIVE, processor.ID).Find(&activeProcessors).Error; err != nil {
			return err
		}
		for _, p := range activeProcessors {
			err = s.obsoleteProcessor(ctx, tx, p)
			if err != nil {
				return err
			}
		}

	}

	// clean up old versions
	var obsoleteProcessors []models.Processor
	if err = tx.Order("version desc").Where("project_id = ? and version_state not in ?", processor.ProjectID, []protos.ProcessorVersionState{
		protos.ProcessorVersionState_PENDING,
		protos.ProcessorVersionState_ACTIVE,
	}).Find(&obsoleteProcessors).Error; err != nil {
		if len(obsoleteProcessors) > MaxObsoleteVersion {
			for _, p := range obsoleteProcessors[MaxObsoleteVersion:] {
				if err = tx.Delete(&p).Error; err != nil {
					return err
				}
			}
		}
	}

	var communityProject modelscommon.CommunityProject
	if tx.Model(&modelscommon.CommunityProject{}).Where("project_id = ?", processor.ProjectID).
		First(&communityProject).Error == nil {
		// the processor has community project, so we need set up a flag for community project updates.
		mapper.TriggerDashTableRefresh(ctx, s.redisClient)
	}

	// start new version
	if processor.VersionState == int32(protos.ProcessorVersionState_PENDING) ||
		processor.VersionState == int32(protos.ProcessorVersionState_ACTIVE) {
		if upgrade {
			return s.restartJob(ctx, processor)
		} else {
			return s.startJob(ctx, processor)
		}
	}

	return nil
}

func (s *Service) obsoleteProcessor(ctx context.Context, tx *gorm.DB, p models.Processor) error {
	p.VersionState = int32(protos.ProcessorVersionState_OBSOLETE)
	p.Pause = false
	err := s.removeProcessor(ctx, tx, p.ID)
	if err != nil {
		return err
	}
	_ = s.notificationClient.RecordProjectNotification(notification.Processor, notification.Info,
		p.ProjectID,
		commonprotos.NotificationType_PROCESSOR_OBSOLETED,
		"Processor version obsoleted.",
		"version", fmt.Sprintf("%d", p.Version),
	)
	return nil
}

func (s *Service) requireProcessorAccess(
	ctx context.Context,
	processorID string,
	permission auth.Action,
) (*models.Processor, error) {
	var processor models.Processor
	if result := s.db.Preload(clause.Associations).First(&processor, "id = ?", processorID); result.Error != nil {
		return nil, result.Error
	}
	if _, err := s.auth.RequiredLoginForProjectID(ctx, processor.ProjectID, permission); err != nil {
		return nil, err
	}
	return &processor, nil
}

type Properties struct {
	project *modelscommon.Project
}

func (p Properties) GetProject() *modelscommon.Project {
	return p.project
}

func NewProperties(project *modelscommon.Project) Properties {
	return Properties{
		project: project,
	}
}

type ShardingController struct {
	tierMapping    map[int32]int32
	projectMapping map[string]int32
	dft            int32
}

func (s ShardingController) Pick(project *modelscommon.Project) int32 {
	if r, has := s.projectMapping[project.ID]; has {
		return r
	}
	var tier int32 // 0 is FREE
	switch project.OwnerType {
	case modelscommon.ProjectOwnerTypeOrg:
		if org := project.OwnerAsOrg; org != nil {
			tier = org.Tier
		}
	case modelscommon.ProjectOwnerTypeUser:
		if user := project.OwnerAsUser; user != nil {
			tier = user.Tier
		}
	}
	if r, has := s.tierMapping[tier]; has {
		return r
	}
	return s.dft
}

func newShardingController(config string) ShardingController {
	s := ShardingController{
		tierMapping:    make(map[int32]int32),
		projectMapping: make(map[string]int32),
	}
	hasDefault := false
	for _, sec := range strings.Split(config, ",") {
		sec = strings.TrimSpace(sec)
		if sec == "" {
			continue
		}
		target, _cid, _ := strings.Cut(sec, ":")
		cid, _ := strconv.ParseInt(_cid, 10, 32)
		if strings.EqualFold(strings.TrimSpace(target), "DEFAULT") {
			s.dft = int32(cid)
			hasDefault = true
		} else {
			scope, t, has := strings.Cut(target, "/")
			if !has {
				scope, t = "TIER", target
			}
			switch scope {
			case "TIER":
				found := false
				for tn, tv := range commonprotos.Tier_value {
					if strings.EqualFold(strings.TrimSpace(t), tn) {
						s.tierMapping[tv] = int32(cid)
						found = true
						break
					}
				}
				if !found {
					panic(fmt.Errorf("invalid tier %q", t))
				}
			case "PROJECT":
				s.projectMapping[t] = int32(cid)
			default:
				panic(fmt.Errorf("invalid scope %q", scope))
			}
		}
	}
	if !hasDefault {
		panic(fmt.Errorf("miss default"))
	}
	return s
}

func (s *Service) createOrUpdateProcessor(
	ctx context.Context,
	identity *modelscommon.Identity,
	project *modelscommon.Project,
	continueFrom int32,
	sentioProperties models.SentioProcessorProperties,
	subgraphProperties models.SubgraphProcessorProperties,
) (p *models.Processor, err error) {
	_, logger := log.FromContext(ctx)
	var clickhouseShardingIndex int32 = 0
	if s.clickhouseMultiSharding != nil {
		clickhouseShardingIndex = s.clickhouseMultiSharding.Pick(NewProperties(project))
	}
	sentioProperties.TimescaleShardingIndex = s.timescaleMultiClient.GetPreferredClient(
		ctx, timescale.MultiClientParameters{Project: project})

	newProcessorClusterID := s.processorClusterSharding.Pick(project)
	vars, err := s.repository.GetProjectVariables(ctx, project.ID)
	if err != nil {
		return nil, err
	}
	const k8sClusterIDEnv = "SYS_K8S_CLUSTER_ID"
	for _, v := range vars {
		if v.Key == k8sClusterIDEnv {
			cid, parseErr := strconv.ParseInt(v.Value, 0, 32)
			if parseErr != nil {
				logger.Warnfe(parseErr, "value of project var %s is %s, not a integer", k8sClusterIDEnv, v.Value)
			} else if _, has := s.k8sClient[int(cid)]; !has {
				logger.Warnf("value of project var %s is %d, out of range", k8sClusterIDEnv, cid)
			} else {
				logger.Infof("using project specified k8sClusterID %d", cid)
				newProcessorClusterID = int32(cid)
			}
		}
	}

	err = s.db.Transaction(func(tx *gorm.DB) error {
		p, err = s.repository.CreateOrUpdateProcessor(
			ctx,
			tx,
			project,
			continueFrom,
			identity,
			clickhouseShardingIndex,
			newProcessorClusterID,
			sentioProperties,
			subgraphProperties,
		)
		if err != nil {
			return err
		}
		return s.activateProcessor(ctx, tx, p, continueFrom > 0)
	})

	if err != nil {
		_ = s.notificationClient.RecordProjectNotification(notification.Processor, notification.Error,
			project.ID,
			commonprotos.NotificationType_PROCESSOR_UPLOAD_FAILED,
			"processor code uploaded failed",
			"error", err.Error())
		return nil, err
	}

	_ = s.notificationClient.RecordProjectNotification(notification.Processor, notification.Info,
		project.ID,
		commonprotos.NotificationType_PROCESSOR_UPLOAD_SUCCESS,
		"Processor code uploaded successfully.",
		"version", fmt.Sprintf("%d", p.Version),
		"processor_status", protos.ProcessorVersionState_name[p.VersionState],
	)
	return p, nil
}

func (s *Service) GetSystemStat(ctx context.Context, req *protos.GetSystemStatRequest) (*protos.GetSystemStatResponse, error) {
	processorID := req.ProcessorId
	processor, err := s.requireProcessorAccess(ctx, processorID, auth.READ)
	if err != nil {
		return nil, err
	}
	if len(processor.ReferenceProjectID) > 0 {
		rp, err := s.repository.ResolveReferenceProcessor(ctx, processor)
		if err != nil {
			return nil, err
		}
		if rp != nil {
			processorID = rp.ID
		}
	}

	driverJobPods, err := s.k8sClient.ListDriverJobPodByProcessor(ctx, processorID)
	if err != nil {
		return nil, err
	}

	var results []*protos.GetSystemStatResponse_MetricData
	promCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	start := req.StartTime.AsTime()
	end := req.EndTime.AsTime()
	step := getStep(start, end)

	for clusterID, pods := range driverJobPods {
		for _, pod := range pods {
			v1api := v1.NewAPI(s.promClient[clusterID])
			query := ""
			if req.GetType() == protos.SystemStatType_CPU {
				query = fmt.Sprintf(`sum(rate(container_cpu_usage_seconds_total{namespace="%s", pod="%s", image!=""}[1m])) by (container)`, pod.Namespace, pod.Name)
			} else if req.GetType() == protos.SystemStatType_MEM {
				query = fmt.Sprintf(`sum(container_memory_working_set_bytes{namespace="%s", pod="%s", image!=""}) by (container)`, pod.Namespace, pod.Name)
			}
			result, _, err := v1api.QueryRange(promCtx, query, v1.Range{
				Start: start,
				End:   end,
				Step:  step,
			})
			if err != nil {
				return nil, err
			}

			ret, err := promResultToProto(result)
			if err != nil {
				return nil, err
			}

			results = append(results, ret...)
		}
	}

	return &protos.GetSystemStatResponse{
		Results: results,
	}, nil
}

func (s *Service) GetChainBlockStat(ctx context.Context, req *protos.GetChainBlockStatRequest) (*protos.GetChainBlockStatResponse, error) {
	processorID := req.ProcessorId
	processor, err := s.requireProcessorAccess(ctx, processorID, auth.READ)
	if err != nil {
		return nil, err
	}
	if len(processor.ReferenceProjectID) > 0 {
		rp, err := s.repository.ResolveReferenceProcessor(ctx, processor)
		if err != nil {
			return nil, err
		}
		if rp != nil {
			processorID = rp.ID
		}
	}

	namespace := s.k8sClient[int(processor.K8sClusterID)].Namespace
	var queries []string
	start := req.StartTime.AsTime()
	end := req.EndTime.AsTime()
	step := getStep(start, end)
	queries = append(queries, fmt.Sprintf(`max by(chain_id) (last_over_time(driver_processed_block_number{service.namespace="%s", processor_id="%s"})[24h])`, namespace, processorID))
	for _, chainState := range processor.ChainStates {
		queries = append(queries, fmt.Sprintf(`max by(chainID) (last_over_time(observer.range{chainID="%s", dimension=~"public|external|external-free"})[24h])`, chainState.ChainID))
	}
	var results []*protos.GetSystemStatResponse_MetricData
	var headResults []*protos.GetSystemStatResponse_MetricData
	promCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	for index, query := range queries {
		v1api := v1.NewAPI(s.promClient[int(processor.K8sClusterID)])
		result, _, err := v1api.QueryRange(promCtx, query, v1.Range{
			Start: start,
			End:   end,
			Step:  step,
		})
		if err != nil {
			return nil, err
		}

		ret, err := promResultToProto(result)
		if err != nil {
			return nil, err
		}

		if index == 0 {
			results = append(results, ret...)
		} else {
			headResults = append(headResults, ret...)
		}
	}

	return &protos.GetChainBlockStatResponse{
		Results:     results,
		HeadResults: headResults,
	}, nil
}

func (s *Service) queryPrometheus(ctx context.Context, client *promapi.Client, query string, startTime, endTime time.Time, step time.Duration) ([]*protos.GetSystemStatResponse_MetricData, error) {
	logger := log.WithContext(ctx)
	promCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	v1api := v1.NewAPI(*client)
	logger.Infof("Querying prometheus with query: %s", query)
	result, _, err := v1api.QueryRange(promCtx, query, v1.Range{
		Start: startTime,
		End:   endTime,
		Step:  step,
	})
	if err != nil {
		return nil, err
	}

	logger.Infof("Prometheus query result: %v", result)
	ret, err := promResultToProto(result)
	if err != nil {
		return nil, err
	}

	return ret, nil
}

func (s *Service) GetProcessorMetrics(ctx context.Context, req *protos.GetProcessorMetricsRequest) (*protos.GetProcessorMetricsResponse, error) {
	processorID := req.ProcessorId
	processor, err := s.requireProcessorAccess(ctx, processorID, auth.READ)
	if err != nil {
		return nil, err
	}
	if len(processor.ReferenceProjectID) > 0 {
		rp, err := s.repository.ResolveReferenceProcessor(ctx, processor)
		if err != nil {
			return nil, err
		}
		if rp != nil {
			processorID = rp.ID
		}
	}

	start := req.StartTime.AsTime()
	end := req.EndTime.AsTime()
	interval := req.GetInterval()
	if interval == "" {
		interval = getInterval(start, end)
	}
	step := intervalToStep(interval)

	promCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	promClient := s.promClient[int(processor.K8sClusterID)]

	// driver reorg count
	driverReorgResults, err := s.queryPrometheus(promCtx, &promClient, fmt.Sprintf(`avg by(chain_id) (increase(driver_reorg_count{processor_id="%v"}[%s]))`, processorID, interval), start, end, step)
	if err != nil {
		return nil, err
	}

	// template instance count
	templateInstanceResults, err := s.queryPrometheus(promCtx, &promClient, fmt.Sprintf(`avg by(chain_id) (processor_template_instance_count{processor_id="%v"})`, processorID), start, end, step)
	if err != nil {
		return nil, err
	}

	// driver data emitted
	dataEmittedResults, err := s.queryPrometheus(promCtx, &promClient, fmt.Sprintf(`avg by(type, subtype, chain_id) (increase(driver_data_emitted{processor_id="%v"}[%s]))`, processorID, interval), start, end, step)
	if err != nil {
		return nil, err
	}

	// average handler duration
	avgHandlerDurationResults, err := s.queryPrometheus(promCtx, &promClient,
		fmt.Sprintf(`avg by(handler, category, chain_id) (increase(processor_handler_duration_sum{processor_id="%v"}[%s]) / increase(processor_handler_duration_count{processor_id="%v"}[%s]))`, processorID, interval, processorID, interval),
		start, end, step)
	if err != nil {
		return nil, err
	}

	// total handler duration
	totalHandlerDurationResults, err := s.queryPrometheus(promCtx, &promClient,
		fmt.Sprintf(`avg by(handler,category,chain_id) (increase(processor_handler_duration_sum{processor_id="%v"}[%s]))`, processorID, interval),
		start, end, step)
	if err != nil {
		return nil, err
	}

	// average rpc duration
	avgRPCDurationResults, err := s.queryPrometheus(promCtx, &promClient,
		fmt.Sprintf(`avg by(handler, category, succeed, chain_id) (increase(processor_rpc_duration_sum{processor_id="%v"}[%s]) / increase(processor_rpc_duration_count{processor_id="%v"}[%s]))`, processorID, interval, processorID, interval),
		start, end, step)
	if err != nil {
		return nil, err
	}

	// total rpc duration
	totalRPCDurationResults, err := s.queryPrometheus(promCtx, &promClient,
		fmt.Sprintf(`avg by(handler,category,succeed,chain_id) (increase(processor_rpc_duration_sum{processor_id="%v"}[%s]))`, processorID, interval),
		start, end, step)
	if err != nil {
		return nil, err
	}

	return &protos.GetProcessorMetricsResponse{
		DriverReorg:          driverReorgResults,
		TemplateInstance:     templateInstanceResults,
		DriverData:           dataEmittedResults,
		AvgHandlerDuration:   avgHandlerDurationResults,
		TotalHandlerDuration: totalHandlerDurationResults,
		AvgRpcDuration:       avgRPCDurationResults,
		TotalRpcDuration:     totalRPCDurationResults,
	}, nil
}

// calculate step based on time range, 15s is the minimum step
func getStep(start, end time.Time) time.Duration {
	step := time.Duration(15) * time.Second
	timeRange := end.Sub(start)
	if timeRange > 2*time.Hour {
		step = time.Duration(1) * time.Minute
	}
	if timeRange > 24*time.Hour {
		step = time.Duration(5) * time.Minute
	}
	if timeRange > 7*24*time.Hour {
		step = time.Duration(1) * time.Hour
	}
	if timeRange > 365*24*time.Hour {
		step = time.Duration(24) * time.Hour
	}
	return step
}

func getInterval(start, end time.Time) string {
	interval := "1m"
	timeRange := end.Sub(start)
	if timeRange >= 1*time.Hour {
		interval = "5m"
	}
	if timeRange >= 3*time.Hour {
		interval = "10m"
	}
	if timeRange >= 24*time.Hour {
		interval = "1h"
	}
	if timeRange >= 7*24*time.Hour {
		interval = "6h"
	}
	if timeRange >= 30*24*time.Hour {
		interval = "12h"
	}
	if timeRange >= 365*24*time.Hour {
		interval = "1d"
	}
	return interval
}

func intervalToStep(interval string) time.Duration {
	unit := interval[len(interval)-1:]
	multiplier := time.Duration(0)

	switch unit {
	case "m":
		multiplier = time.Minute
	case "h":
		multiplier = time.Hour
	case "d":
		multiplier = 24 * time.Hour
	default:
		return time.Minute
	}

	valueStr := interval[:len(interval)-1]
	value, err := strconv.Atoi(valueStr)
	if err != nil {
		return time.Minute
	}

	return time.Duration(value) * multiplier
}

func promResultToProto(value model.Value) ([]*protos.GetSystemStatResponse_MetricData, error) {
	var result []*protos.GetSystemStatResponse_MetricData

	switch v := value.(type) {
	case model.Matrix:
		for _, stream := range v {
			metric := make(map[string]string)
			for k, v := range stream.Metric {
				if k == "chain_id" {
					k = "chainID"
				}
				metric[string(k)] = string(v)
			}
			if len(metric) == 0 {
				continue
			}

			var values []*protos.GetSystemStatResponse_DataPoint
			for _, point := range stream.Values {
				values = append(values, &protos.GetSystemStatResponse_DataPoint{
					Timestamp: strconv.FormatInt(point.Timestamp.Unix(), 10),
					Value:     point.Value.String(),
				})
			}

			result = append(result, &protos.GetSystemStatResponse_MetricData{
				Metric: metric,
				Values: values,
			})
		}
	default:
		return nil, fmt.Errorf("unsupported model.Value type: %T", v)
	}

	return result, nil
}

func (s *Service) GetProcessorProfile(ctx context.Context, req *protos.GetProcessorProfileRequest) (*protos.GetProcessorProfileResponse, error) {
	var processor models.Processor
	if result := s.db.Preload(clause.Associations).First(&processor, "id = ?", req.ProcessorId); result.Error != nil {
		return nil, result.Error
	}
	if _, _, err := s.auth.RequiredLoginForProject(ctx, processor.ProjectID, auth.READ); err != nil {
		return nil, err
	}

	driverJobPods, err := s.k8sClient.ListDriverJobPodByProcessor(ctx, req.ProcessorId)
	if err != nil {
		return nil, err
	}

	// TODO here need to consider pod cluster
	for _, pods := range driverJobPods {
		for _, pod := range pods {
			podName := pod.Name
			podIP := pod.Status.PodIP
			// check if podName ends with "processor"
			if strings.HasSuffix(podName, "processor") && podIP != "" {
				var url string
				if req.Heap {
					url = fmt.Sprintf("http://%s:4040/heap", podIP)
				} else {
					queryTime := req.GetTime()
					url = fmt.Sprintf("http://%s:4040/profile", podIP)
					if queryTime != 0 {
						url = fmt.Sprintf("%s?t=%d", url, queryTime)
					}
				}
				resp, err := http.Get(url)
				if err != nil {
					return nil, err
				}
				if resp.StatusCode != 200 {
					return nil, fmt.Errorf("get profile failed, status code: %d", resp.StatusCode)
				}
				defer func() {
					if err := resp.Body.Close(); err != nil {
						log.Fatale(err)
					}
				}()
				body, err := io.ReadAll(resp.Body)
				if err != nil {
					return nil, err
				}

				currentTime := time.Now().Unix()
				objectName := fmt.Sprintf("profiles/%s/%d.json", req.ProcessorId, currentTime)
				obj := s.gcsClient.Bucket(s.gcsBucket).Object(objectName)
				w := obj.NewWriter(ctx)
				gz := gzip.NewWriter(w)
				w.ContentType = "application/json"
				w.ContentEncoding = "gzip"
				_, err = gz.Write(body)
				if err != nil {
					return nil, err
				}
				_ = gz.Close()
				_ = w.Close()
				opts := &storage.SignedURLOptions{
					GoogleAccessID: s.gcsServiceAccount,
					Scheme:         storage.SigningSchemeV4,
					Method:         "GET",
					Expires:        time.Now().Add(72 * time.Hour),
				}
				profileURL, err := s.gcsClient.Bucket(s.gcsBucket).SignedURL(objectName, opts)
				if err != nil {
					return nil, err
				}
				// var jsonResponse map[string]interface{}
				// if err := json.Unmarshal(body, &jsonResponse); err != nil {
				//	log.Fatalf("Failed to unmarshal JSON: %v", err)
				// }
				//
				// protoStruct, err := structpb.NewStruct(jsonResponse)
				// if err != nil {
				//	log.Fatalf("Failed to convert to protobuf Struct: %v", err)
				// }

				return &protos.GetProcessorProfileResponse{
					ProfileUrl: profileURL,
					// ProfileData: protoStruct,
				}, nil
			}
		}
	}

	return nil, fmt.Errorf("no processor pod found")
}

func (s *Service) SetProcessorEntitySchema(ctx context.Context, req *protos.SetProcessorEntitySchemaRequest) (*emptypb.Empty, error) {
	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var processor models.Processor
		err := tx.Model(&processor).Where("id = ?", req.GetProcessorId()).First(&processor).Error
		if err != nil {
			return err
		}
		if req.GetSchema() == processor.EntitySchema {
			return nil
		}
		processor.EntitySchema = req.GetSchema()
		return tx.Save(&processor).Error
	})
	return &emptypb.Empty{}, err
}

func NewMultiPromClient(file string) (map[int]promapi.Client, error) {
	type Config struct {
		ClusterID int    `json:"ClusterID" yaml:"ClusterID"`
		PromAddr  string `json:"PromAddr" yaml:"PromAddr"`
	}
	var configs []Config
	err := utils.LoadFile("multi prom client config", file, &configs)
	if err != nil {
		return nil, err
	}
	clients := make(map[int]promapi.Client)
	for _, conf := range configs {
		clients[conf.ClusterID], err = promapi.NewClient(promapi.Config{
			Address: conf.PromAddr,
		})
		if err != nil {
			return nil, fmt.Errorf("new prom client with address %q failed: %w", conf.PromAddr, err)
		}
	}
	return clients, nil
}

func (s *Service) PauseProcessor(ctx context.Context, req *protos.GetProcessorRequest) (*emptypb.Empty, error) {
	return s.updateProcessorPause(ctx, req.ProcessorId, true)
}

func (s *Service) ResumeProcessor(ctx context.Context, req *protos.GetProcessorRequest) (*emptypb.Empty, error) {
	return s.updateProcessorPause(ctx, req.ProcessorId, false)
}

func (s *Service) updateProcessorPause(ctx context.Context, processorID string, pause bool) (*emptypb.Empty, error) {
	processor, err := s.requireProcessorAccess(ctx, processorID, auth.WRITE)
	if err != nil {
		return nil, err
	}
	if !processor.IsRunningVersion() {
		return nil, status.Error(codes.InvalidArgument, "processor version state is not active or pending")
	}
	if len(processor.ReferenceProjectID) > 0 {
		return nil, status.Error(codes.InvalidArgument, "reference processor can not be paused")
	}
	processor.Pause = pause
	if err = s.db.WithContext(ctx).Save(processor).Error; err != nil {
		return nil, err
	}
	if err = s.StartOrUpdateDriverJob(ctx, processor); err != nil {
		log.Errorfe(err, "failed to update driverJob for processor %q", processorID)
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *Service) UpdateEventlogMigrateStatus(ctx context.Context, req *protos.UpdateEventlogMigrateStatusRequest) (*emptypb.Empty, error) {
	migrateStatus := models.ToEventlogMigrateStatus(req.GetEventlogMigrateStatus())
	var newSharding *int32
	if migrateStatus == models.EventlogMigrateStatus_Migrated {
		newSharding = lo.ToPtr[int32](req.GetEventlogNewShardIndex())
	}
	if err := s.repository.UpdateEventlogMigrateStatus(ctx, req.GetProcessorId(), migrateStatus, newSharding); err != nil {
		return nil, err
	}
	log.Infof("update eventlog migrate status for processor %q to %v, new sharding: %d",
		req.GetProcessorId(), migrateStatus, req.GetEventlogNewShardIndex())
	return &emptypb.Empty{}, nil
}

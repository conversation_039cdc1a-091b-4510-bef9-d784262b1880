package handler

import (
	"sentioxyz/sentio/common/identifier"
	"sentioxyz/sentio/service/analytic/query"
	"sentioxyz/sentio/service/analytic/query/user"
	protoscommon "sentioxyz/sentio/service/common/protos"

	"google.golang.org/protobuf/types/known/timestamppb"
)

func nilCheck(left, right *user.AggregateResult) *user.AggregateResult {
	if left == nil {
		return right
	}
	if right == nil {
		return left
	}
	return nil
}

func intersect(left, right *user.AggregateResult) *user.AggregateResult {
	if r := nilCheck(left, right); r != nil {
		return r
	}
	newResult := user.NewAggregateResult()
	for k := range left.HashDictReverse {
		if _, ok := right.HashDictReverse[k]; ok {
			newResult.AddUniqueUser(k)
		}
	}
	for leftTime, leftSeries := range left.TimeSeriesData {
		if rightSeries, ok := right.TimeSeriesData[leftTime]; ok {
			newResult.TimeSeriesData[leftTime] = leftSeries
			newResult.TimeSeriesData[leftTime].And(rightSeries)
		}
	}
	return newResult
}

func union(left, right *user.AggregateResult) *user.AggregateResult {
	if r := nilCheck(left, right); r != nil {
		return r
	}
	newResult := user.NewAggregateResult()
	for k := range left.HashDictReverse {
		newResult.AddUniqueUser(k)
	}
	for k := range right.HashDictReverse {
		newResult.AddUniqueUser(k)
	}
	for leftTime, leftSeries := range left.TimeSeriesData {
		if rightSeries, ok := right.TimeSeriesData[leftTime]; ok {
			newResult.TimeSeriesData[leftTime] = leftSeries
			newResult.TimeSeriesData[leftTime].Or(rightSeries)
		} else {
			newResult.TimeSeriesData[leftTime] = leftSeries
		}
	}
	return newResult
}

func initComputeStats() *protoscommon.ComputeStats {
	return &protoscommon.ComputeStats{
		ComputedAt:        timestamppb.Now(),
		BinaryVersionHash: identifier.BinaryHash(),
		ComputedBy:        "web_service",
	}
}

func getK8sCluster(args *query.Args) string {
	if args.Processor == nil {
		return "sentio-sea"
	}
	return args.ClickhouseMultiSharding.GetShard(args.Processor.ClickhouseShardingIndex).GetK8sCluster()
}

from service.ai.api import api_pb2
from typing import List, Dict, Tuple
from langchain.schema.runnable import <PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, BaseMessage
from pydantic import BaseModel, Field
from typing import List as TypedList
from uuid import UUID
from service.ai.server.models.model_registry import ModelRegistry
SYSTEM_PROMPT = """You are a helpful table selection assistant that identifies which database tables are relevant to a user's query about blockchain data.

<Guidelines>
1. Analyze the user's question or request to understand what blockchain data they need.
2. Select tables that are directly or indirectly relevant to answering their query.
3. Prioritize tables in the following order:
   a. Project tables
   b. View tables
   c. External project tables
   d. System tables
   e. Reserved tables (lowest priority)
4. System and Reserved tables are not belong to the project.
</Guidelines>

<Relevance Criteria>
- The main entities in the user's query (transactions, events, contracts, etc.)
- Relationships between tables when selecting (e.g., if joining would be necessary).
- Specific blockchains or tokens mentioned
- The type of analysis requested (volume analysis, price trends, etc.)
- Tables that could provide supplementary information for a more comprehensive answer
</Relevance Criteria>
"""

# Define the Pydantic model for our table selection output
class TableSelectionOutput(BaseModel):
    selected_tables: TypedList[str] = Field(
        description="List of table names that are relevant to the user's query"
    )

class TableSelectionAgent:
    def __init__(self, model_registry: ModelRegistry):
        self.model_registry = model_registry

    def select_tables(self, messages: List[Dict[str, str]], all_tables_metadata: str, default_tables: TypedList[str], run_id: UUID):
        """
        Analyze a user query and select relevant tables
        
        Args:
            query: The user's natural language query
            all_tables_metadata: Optional metadata about all available tables
            
        Returns:
            A dict with selected tables and any clarification questions
        """
        # Prepare system prompt with additional context if metadata is provided
        system_content = SYSTEM_PROMPT
        if all_tables_metadata:
            system_content += "\n\n**Available Tables:**\n" + all_tables_metadata

        # Convert messages to LangChain format
        langchain_messages = [SystemMessage(content=system_content)]
    
        for msg in messages:
            if msg.role == api_pb2.Message.Role.USER:
                langchain_messages.append(HumanMessage(content=msg.content))
            elif msg.role == api_pb2.Message.Role.ASSISTANT:
                langchain_messages.append(AIMessage(content=msg.content))

        return self.chat_inner(langchain_messages, default_tables, run_id)

    def chat_inner(self, messages: List[BaseMessage], default_tables: TypedList[str], run_id: UUID) -> TypedList[str]:
        try:
            # Create the LangChain ChatOpenAI instance with structured output
            llm = self.model_registry.get_gpt41_mini(temperature=0.1, max_tokens=1000)

            # Create a structured output chain using the Pydantic model
            structured_llm = llm.with_structured_output(TableSelectionOutput)
            
            # Invoke the model with structured output
            output: TableSelectionOutput = RunnableLambda(lambda x: structured_llm.invoke(x)).invoke(messages, {"run_id": run_id})
            
            # Get the selected tables from the structured output
            selected_tables = output.selected_tables
            
            # We should always return the default tables once any table in the AI selected tables is not part of the default tables
            for table in selected_tables:
                if table not in default_tables:
                    print(f"Table {table} is not part of the default tables. Returning default tables.")
                    return default_tables
            return selected_tables

        except Exception as e:
            # Return a safe default response in case of error
            error_message = f"Error in table selection: {str(e)}"
            print(error_message)  # Log the error
      
        return default_tables

package common

import (
	"time"

	processorProtos "sentioxyz/sentio/service/processor/protos"
)

const (
	TestCaseStatePending   = "pending"
	TestCaseStateRunning   = "running"
	TestCaseStateFinished  = "finished"
	TestCaseStateCleanedUp = "cleanedUp"
)

type TestCaseRunState struct {
	TestCaseRunID string
	TestCaseName  string
	PodName       string
	Namespace     string
	Started       time.Time
	Finished      time.Time
	State         string
	Reason        string
	Failure       bool
	DriverStatus  DriverStatus
}

type TestSuiteRunState struct {
	Config         TestSuiteConfig
	TestSuiteRunID string
	TestSuiteName  string
	Namespace      string
	Started        time.Time
	Finished       time.Time
	IsFinished     bool
	TestCaseRuns   map[string]*TestCaseRunState
	Trigger        string
}

type TestManagerState struct {
	ActiveTestSuiteRuns map[string]*TestSuiteRunState
}

type ProxyConfig struct {
	TestSuiteRunID    string
	TestSuiteName     string
	TestSuiteRunState *TestSuiteRunState
	TestCaseRunID     string
	TestCaseName      string
	TestCaseRunState  *TestCaseRunState
	HeartbeatInterval time.Duration
}

type StageDurationMap map[string]time.Duration

type DriverStatus struct {
	PodRestartCount           int32
	ReceivedGetProcessor      bool
	ReceivedDownloadProcessor bool
	ReceivedInitialChainState bool
	FaultInjectionCount       uint64
	ChainState                []*processorProtos.ChainState
	ChainStageDuration        map[string]StageDurationMap
	LastHeartbeatTime         time.Time
	ServiceLatencyMeters      map[string]map[string][]byte
	ChainProgressMeters       map[string][][]uint64
	LastLog                   string
}

type TestResult struct {
	TestSuiteName     string
	TestCaseName      string
	TestCaseConfig    TestCaseConfig
	TestSuiteRunID    string
	TestCaseRunID     string
	TestSuiteConfig   TestSuiteConfig
	OutputMap         map[string]interface{}
	Failure           bool
	Reason            string
	Started           time.Time
	Finished          time.Time
	FinalDriverStatus DriverStatus
}

const (
	GcsMetadataKeySuiteName  = "x-sentio-test-suite-name"
	GcsMetadataKeyCaseName   = "x-sentio-test-case-name"
	GcsMetadataKeySuiteRunID = "x-sentio-test-suite-run-id"
	GcsMetadataKeyCaseRunID  = "x-sentio-test-case-run-id"
	GcsMetadataKeyStatus     = "x-sentio-test-status"
	GcsMetadataStatusPass    = "pass"
	GcsMetadataStatusFail    = "fail"
)

package subgraph

import (
	"bytes"
	"context"
	_ "embed"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/stretchr/testify/assert"

	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/utils"
	"sentioxyz/sentio/common/wasm"
	"sentioxyz/sentio/driver/subgraph/common"
	"sentioxyz/sentio/driver/subgraph/ethereum"
)

//go:embed testdata/build/main/main.wasm
var testModBytes []byte

//go:embed testdata/build/main/abis/foo.json
var testContractABICnt []byte

type mockEntityStore struct {
	abi      abi.ABI
	entities map[string]map[string]*common.Entity
}

func (s *mockEntityStore) InitEntitySchema(ctx context.Context) error {
	panic("not implemented")
}

func (s *mockEntityStore) EraseEntitySchema(ctx context.Context) error {
	panic("not implemented")
}

func (s *mockEntityStore) ReorgEntities(ctx context.Context, blockNumber int64) error {
	panic("not implemented")
}

func (s *mockEntityStore) TxnBegin(_, _ time.Time) {
	panic("not implemented")
}

func (s *mockEntityStore) TxnFinish(ctx context.Context, commit bool) (map[string]int, map[string]int, error) {
	panic("not implemented")
}

func (s *mockEntityStore) SetEntity(
	ctx *wasm.CallContext[CtxData],
	name *wasm.String,
	id *wasm.String,
	entity *common.Entity,
) {
	ctx.TopParams().Logger.Infof("called 'store.set' name:%s, id:%s, got:%s", name, id, entity.Text(","))
	utils.PutIntoK2Map(s.entities, name.String(), id.String(), entity)
}

func (s *mockEntityStore) GetEntity(ctx *wasm.CallContext[CtxData], name *wasm.String, id *wasm.String) *common.Entity {
	e, _ := utils.GetFromK2Map(s.entities, name.String(), id.String())
	return e
}

func (s *mockEntityStore) GetEntityInBlock(ctx *wasm.CallContext[CtxData], name *wasm.String, id *wasm.String) *common.Entity {
	panic("not implemented")
}

func (s *mockEntityStore) GetRelatedEntities(ctx *wasm.CallContext[CtxData], name, id, field *wasm.String) *wasm.ObjectArray[*common.Entity] {
	panic("not implemented")
}

func (s *mockEntityStore) RemoveEntity(ctx *wasm.CallContext[CtxData], name *wasm.String, id *wasm.String) {
	panic("not implemented")
}

func (s *mockEntityStore) GetNetwork(ctx *wasm.CallContext[CtxData]) *wasm.String {
	panic("not implemented")
}

func (s *mockEntityStore) EthCall(ctx *wasm.CallContext[CtxData], call *ethereum.SmartContractCall) *wasm.ObjectArray[*ethereum.Value] {
	logger := ctx.TopParams().Logger
	logger.Infof("called 'ethereum.call'")
	logger.Infof("call.ContractName: %s", call.ContractName.String())
	logger.Infof("call.ContractAddress: %s", call.ContractAddress.String())
	logger.Infof("call.FunctionName: %s", call.FunctionName.String())
	logger.Infof("call.FunctionSignature: %s", call.FunctionSignature.String())
	var method abi.Method
	for _, md := range s.abi.Methods {
		if strings.HasPrefix(call.FunctionSignature.String(), md.Sig+":") {
			method = md
			break
		}
	}
	var vals []any
	for i, param := range call.FunctionParams.Data {
		input := method.Inputs[i]
		val := param.ToGoType(input.Type)
		vals = append(vals, val)
		logger.Infof("call.FunctionParams[%d]: %d/%T/%v/%T/%v", i, param.Kind, param.Value, param.Value, val, val)
	}
	data, err := method.Inputs.Pack(vals...)
	if err != nil {
		panic(fmt.Errorf("pack input for eth_call failed: %w", err))
	}
	logger.Infof("data: %x", append(method.ID, data...))

	return &wasm.ObjectArray[*ethereum.Value]{
		Data: []*ethereum.Value{{
			Kind:  ethereum.ValueKindInt,
			Value: common.MustBuildBigInt(111222),
		}},
	}
}

func (s *mockEntityStore) EthEncode(ctx *wasm.CallContext[CtxData], value *ethereum.Value) *wasm.ByteArray {
	panic("not implemented")
}

func (s *mockEntityStore) EthDecode(ctx *wasm.CallContext[CtxData], types *wasm.String, data *wasm.ByteArray) *ethereum.Value {
	panic("not implemented")
}

func (s *mockEntityStore) CreateDataSource(ctx *wasm.CallContext[CtxData], tplName *wasm.String, params *wasm.ObjectArray[*wasm.String]) {
	ctx.TopParams().Logger.Infof("called 'dataSource.create', tplName:%s, len(params):%d, params[0]:%s",
		tplName.String(), len(params.Data), params.Data[0].String())
}

func (s *mockEntityStore) CreateDataSourceWithCtx(
	ctx *wasm.CallContext[CtxData],
	tplName *wasm.String,
	params *wasm.ObjectArray[*wasm.String],
	ctxEntity *common.Entity,
) {
	ctx.TopParams().Logger.Infof("called 'dataSource.createWithContext', tplName:%s, len(params):%d, params[0]:%s",
		tplName.String(), len(params.Data), params.Data[0].String())
	for _, pr := range ctxEntity.Properties.Data {
		ctx.TopParams().Logger.Infof("key:%s, value:%d/%v", pr.Key, pr.Value.Kind, pr.Value.Value)
	}
}

func (s *mockEntityStore) GetNewDataSources() []DataSource {
	return nil
}

func prepare(t *testing.T) (*wasm.Instance[CtxData], *mockEntityStore) {
	contractABI, err := abi.JSON(bytes.NewReader(testContractABICnt))
	if err != nil {
		t.Fatal(err)
	}
	m := wasm.NewInstance[CtxData]("test", testModBytes, 1<<30)
	s := &mockEntityStore{abi: contractABI, entities: make(map[string]map[string]*common.Entity)}
	importFunctions(m, s, nil)
	return m, s
}

func Test_handleFoo(t *testing.T) {
	m, s := prepare(t)

	//export function handleFoo(event: FooEvent): void {
	//  log.info("enter handlerTransfer", []);
	//  let entity = new Bar(buildID(event))
	//  log.info("step-1 built entity", []);
	//  entity._from = event.params._from
	//  entity._to = event.params._to
	//  entity._value = event.params._value
	//  log.info("step-2", []);
	//
	//  entity.blockNumber = event.block.number
	//  entity.blockTimestamp = event.block.timestamp
	//  entity.transactionHash = event.transaction.hash
	//  entity.logIndex = event.logIndex
	//
	//  let mcAddr = event.transaction.to
	//  if (mcAddr !== null) {
	//    let mc = FooContract.bind(mcAddr)
	//    entity.srcBalance = mc.getBalance(event.params._from)
	//    entity.dstBalance = mc.getBalance(event.params._to)
	//  }
	//
	//  log.info("step-3, srcBalance = {}, dstBalance = {}", [
	//    entity.srcBalance.toString(),
	//    entity.dstBalance.toString(),
	//  ]);
	//  entity.save()
	//
	//  log.info("blockNumber = {}, logIndex = {}, transactionHash = {}", [
	//    event.block.number.toHexString(),
	//    event.logIndex.toHexString(),
	//    event.transaction.hash.toHexString(),
	//  ]);
	//
	//  TestTpl.create(event.params._to)
	//}
	assert.NoError(t, m.ExportFunction("handleFoo", (func(*ethereum.Event))(nil)))

	assert.NoError(t, m.Init(log.With()))

	event := ethereum.Event{
		Address:             common.MustBuildAddressFromString("******************************************"),
		LogIndex:            common.MustBuildBigInt(123),
		TransactionLogIndex: common.MustBuildBigInt(112233),
		LogType:             wasm.BuildString("good"),
		Block: &ethereum.Block{
			Number:    common.MustBuildBigInt(887766),
			Timestamp: common.MustBuildBigInt(10000000000),
		},
		Transaction: &ethereum.Transaction{
			Hash: &wasm.ByteArray{Data: []byte{127, 4, 3, 2, 1}},
			To:   common.MustBuildAddressFromString("******************************************"),
		},
		Parameters: ethereum.BuildEventParams(
			ethereum.BuildEventParam("_from", &ethereum.Value{
				Kind:  ethereum.ValueKindAddress,
				Value: common.MustBuildAddressFromString("******************************************"),
			}),
			ethereum.BuildEventParam("_to", &ethereum.Value{
				Kind:  ethereum.ValueKindAddress,
				Value: common.MustBuildAddressFromString("******************************************"),
			}),
			ethereum.BuildEventParam("_value", &ethereum.Value{
				Kind:  ethereum.ValueKindInt,
				Value: common.MustBuildBigInt(123456),
			}),
		),
		Receipt: nil,
	}

	_, _, err := m.CallExportFunction(wasm.NewCallContext[CtxData](context.Background()), wasm.CallParams[CtxData]{
		ExportFuncName: "handleFoo",
		Logger:         log.With(),
	}, &event)
	assert.NoError(t, err)

	assert.Equal(t, map[string]map[string]*common.Entity{
		"Bar": {
			"0x7f040302017c000000": common.BuildEntity(
				&common.EntityProperty{
					Key: wasm.BuildString("id"),
					Value: &common.Value{
						Kind:  common.ValueKindString,
						Value: wasm.BuildString("0x7f040302017c000000"),
					},
				},
				&common.EntityProperty{
					Key: wasm.BuildString("_from"),
					Value: &common.Value{
						Kind:  common.ValueKindBytes,
						Value: wasm.MustBuildByteArrayFromHex("******************************************"),
					},
				},
				&common.EntityProperty{
					Key: wasm.BuildString("_to"),
					Value: &common.Value{
						Kind:  common.ValueKindBytes,
						Value: wasm.MustBuildByteArrayFromHex("******************************************"),
					},
				},
				&common.EntityProperty{
					Key: wasm.BuildString("_value"),
					Value: &common.Value{
						Kind:  common.ValueKindBigInt,
						Value: common.MustBuildBigInt(123456),
					},
				},
				&common.EntityProperty{
					Key: wasm.BuildString("blockNumber"),
					Value: &common.Value{
						Kind:  common.ValueKindBigInt,
						Value: common.MustBuildBigInt(887766),
					},
				},
				&common.EntityProperty{
					Key: wasm.BuildString("blockTimestamp"),
					Value: &common.Value{
						Kind:  common.ValueKindBigInt,
						Value: common.MustBuildBigInt(10000000000),
					},
				},
				&common.EntityProperty{
					Key: wasm.BuildString("transactionHash"),
					Value: &common.Value{
						Kind:  common.ValueKindBytes,
						Value: &wasm.ByteArray{Data: []byte{127, 4, 3, 2, 1}},
					},
				},
				&common.EntityProperty{
					Key: wasm.BuildString("logIndex"),
					Value: &common.Value{
						Kind:  common.ValueKindBigInt,
						Value: common.MustBuildBigInt(123),
					},
				},
				&common.EntityProperty{
					Key: wasm.BuildString("srcBalance"),
					Value: &common.Value{
						Kind:  common.ValueKindBigInt,
						Value: common.MustBuildBigInt(111222),
					},
				},
				&common.EntityProperty{
					Key: wasm.BuildString("dstBalance"),
					Value: &common.Value{
						Kind:  common.ValueKindBigInt,
						Value: common.MustBuildBigInt(111222),
					},
				},
			),
		},
	}, s.entities)
}

type testMyEvent struct {
	Array1         *wasm.BaseArray[wasm.Bool]
	Array2         *wasm.BaseArray[wasm.U8]
	Params         *wasm.ObjectArray[*ethereum.EventParam]
	IntKey10       wasm.I8
	IntKey11       wasm.I8
	IntKey12       wasm.I8
	IntKey2        wasm.I16
	IntKey3        wasm.I64
	IntKey4        wasm.I32
	BoolKey        wasm.Bool
	FloatKey1      wasm.F32
	FloatKey2      wasm.F64
	Address        *wasm.ByteArray
	Message        *wasm.String
	IntKey20       wasm.I8
	IntKey21       wasm.I8
	IntKey22       wasm.I8
	BigIntKey1     *common.BigInt
	BigIntKey2     *common.BigInt
	BigIntKey3     *common.BigInt
	BigDecimalKey1 *common.BigDecimal
	BigDecimalKey2 *common.BigDecimal
	BigDecimalKey3 *common.BigDecimal
}

func (ev *testMyEvent) Dump(mm *wasm.MemoryManager) wasm.Pointer {
	return mm.DumpObject(ev)
}

func (ev *testMyEvent) Load(mm *wasm.MemoryManager, p wasm.Pointer) {
	mm.LoadObject(p, ev)
}

func Test_myEvent(t *testing.T) {
	m, _ := prepare(t)

	//export function buildMyEvent(): MyEvent {
	//  let arr1: Array<boolean> = new Array<boolean>(5)
	//  for (let i = 0; i < 5; i++) {
	//    arr1[i] = i % 2 == 0
	//  }
	//  let arr2: Array<u8> = new Array<u8>(10)
	//  for (let i: u8 = 0; i < 10; i++) {
	//    arr2[i] = i + 10
	//  }
	//  let params: Array<ethereum.EventParam> = new Array<ethereum.EventParam>()
	//  params.push(new ethereum.EventParam('param1', ethereum.Value.fromBoolean(true)))
	//  params.push(new ethereum.EventParam('param2', ethereum.Value.fromString('value-2')))
	//  params.push(
	//    new ethereum.EventParam(
	//      'param3',
	//      ethereum.Value.fromAddress(Address.fromBytes(Bytes.fromHexString('0102030405060708090a0b0c0d0e0f1011121314')))
	//    )
	//  )
	//  let addr: Address = Address.fromBytes(Bytes.fromHexString('0102030405060708090a0b0c0d0e0f1011121318'))
	//  return new MyEvent(arr1, arr2, params,
	//    111, 112, 113, 114, 9223372036854775807, 116,
	//    true, 178.125, 0.00125, addr, "message12",
	//    117, 118, 119,
	//    BigInt.fromU32(3333333333),
	//    BigInt.fromI32(-1111111111),
	//    BigInt.zero(),
	//    new BigDecimal(BigInt.fromI32(-1111111111)),
	//    BigDecimal.fromString("11111111.22222222"),
	//    BigDecimal.fromString("-11111111.22222222"))
	//}
	assert.NoError(t, m.ExportFunction("buildMyEvent", (func() *testMyEvent)(nil)))

	//export function processMyEvent(event: MyEvent): MyEvent {
	//  event.message = event.message + '-suffix'
	//  event.floatKey1 += 10000
	//  event.floatKey2 += 20000
	//  event.boolKey = !event.boolKey
	//  event.address = constAddr
	//  event.array2 = null
	//  event.intKey22 = -111
	//  event.bigIntKey3 = event.bigIntKey3.plus(BigInt.fromI32(1111111111))
	//  event.bigDecimalKey3 = BigDecimal.fromString("-12345679134567898.75308642")
	//  return event
	//}
	assert.NoError(t, m.ExportFunction("processMyEvent", (func(*testMyEvent) *testMyEvent)(nil)))

	assert.NoError(t, m.Init(log.With()))

	ret, _, err := m.CallExportFunction(wasm.NewCallContext[CtxData](context.Background()), wasm.CallParams[CtxData]{
		ExportFuncName: "buildMyEvent",
		Logger:         log.With(),
	})
	assert.NoError(t, err)

	retEvent := ret.(*testMyEvent)
	assert.Equal(t, &wasm.BaseArray[wasm.Bool]{Data: []wasm.Bool{true, false, true, false, true}}, retEvent.Array1)
	assert.Equal(t, &wasm.BaseArray[wasm.U8]{Data: []wasm.U8{10, 11, 12, 13, 14, 15, 16, 17, 18, 19}}, retEvent.Array2)
	assert.Equal(t, &wasm.ObjectArray[*ethereum.EventParam]{Data: []*ethereum.EventParam{
		{Name: wasm.BuildString("param1"), Value: &ethereum.Value{
			Kind:  ethereum.ValueKindBool,
			Value: wasm.Bool(true),
		}},
		{Name: wasm.BuildString("param2"), Value: &ethereum.Value{
			Kind:  ethereum.ValueKindString,
			Value: wasm.BuildString("value-2"),
		}},
		{Name: wasm.BuildString("param3"), Value: &ethereum.Value{
			Kind: ethereum.ValueKindAddress,
			Value: &common.Address{
				ByteArray: wasm.ByteArray{
					Data: []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20},
				},
			},
		}},
	}}, retEvent.Params)
	assert.Equal(t, wasm.I8(111), retEvent.IntKey10)
	assert.Equal(t, wasm.I8(112), retEvent.IntKey11)
	assert.Equal(t, wasm.I8(113), retEvent.IntKey12)
	assert.Equal(t, wasm.I16(114), retEvent.IntKey2)
	assert.Equal(t, wasm.I64(9223372036854775807), retEvent.IntKey3)
	assert.Equal(t, wasm.I32(116), retEvent.IntKey4)
	assert.Equal(t, wasm.Bool(true), retEvent.BoolKey)
	assert.Equal(t, wasm.F32(178.125), retEvent.FloatKey1)
	assert.Equal(t, wasm.F64(0.00125), retEvent.FloatKey2)
	assert.Equal(
		t,
		&wasm.ByteArray{Data: []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 24}},
		retEvent.Address,
	)
	assert.Equal(t, "message12", retEvent.Message.String())
	assert.Equal(t, wasm.I8(117), retEvent.IntKey20)
	assert.Equal(t, wasm.I8(118), retEvent.IntKey21)
	assert.Equal(t, wasm.I8(119), retEvent.IntKey22)

	assert.Equal(t, int64(3333333333), retEvent.BigIntKey1.Int64())
	assert.Equal(t, int64(-1111111111), retEvent.BigIntKey2.Int64())
	assert.Equal(t, int64(0), retEvent.BigIntKey3.Int64())
	//fmt.Printf("bigIntKey1: %v %v\n", retEvent.BigIntKey1.String(), retEvent.BigIntKey1.ByteArray.Data)
	//fmt.Printf("bigIntKey2: %v %v\n", retEvent.BigIntKey2.String(), retEvent.BigIntKey2.ByteArray.Data)
	//fmt.Printf("bigIntKey3: %v %v\n", retEvent.BigIntKey3.String(), retEvent.BigIntKey3.ByteArray.Data)

	assert.Equal(t, "-1111111111", retEvent.BigDecimalKey1.String())
	assert.Equal(t, "11111111.22222222", retEvent.BigDecimalKey2.String())
	assert.Equal(t, "-11111111.22222222", retEvent.BigDecimalKey3.String())
	// fmt.Printf("BigDecimalKey1: %v %v %v\n", retEvent.BigDecimalKey1.String(),
	// retEvent.BigDecimalKey1.Digits.ByteArray.Data, retEvent.BigDecimalKey1.Exp.ByteArray.Data)
	// fmt.Printf("BigDecimalKey2: %v %v %v\n", retEvent.BigDecimalKey2.String(),
	// retEvent.BigDecimalKey2.Digits.ByteArray.Data, retEvent.BigDecimalKey2.Exp.ByteArray.Data)
	// fmt.Printf("BigDecimalKey3: %v %v %v\n", retEvent.BigDecimalKey3.String(),
	// retEvent.BigDecimalKey3.Digits.ByteArray.Data, retEvent.BigDecimalKey3.Exp.ByteArray.Data)

	ret2, _, err := m.CallExportFunction(wasm.NewCallContext[CtxData](context.Background()), wasm.CallParams[CtxData]{
		ExportFuncName: "processMyEvent",
		Logger:         log.With(),
	}, retEvent)
	assert.NoError(t, err)

	retEvent2 := ret2.(*testMyEvent)
	assert.Equal(t, "message12-suffix", retEvent2.Message.String())
	assert.Equal(t, wasm.F32(10178.125), retEvent2.FloatKey1)
	assert.Equal(t, wasm.F64(20000.00125), retEvent2.FloatKey2)
	assert.Equal(t, wasm.Bool(false), retEvent2.BoolKey)
	assert.Equal(
		t,
		&wasm.ByteArray{Data: []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 25}},
		retEvent2.Address,
	)
	assert.Nil(t, retEvent2.Array2)
	assert.Equal(t, wasm.I8(-111), retEvent2.IntKey22)
	assert.Equal(t, int64(4444444444), retEvent2.BigIntKey3.Int64())
	assert.Equal(t, "-12345679134567898.75308642", retEvent2.BigDecimalKey3.String())
}

func Test_processEntity(t *testing.T) {
	m, _ := prepare(t)

	//export function processBar(bar: Bar): Bar {
	//  bar._value = BigInt.fromI32(321)
	//  return bar
	//}
	assert.NoError(t, m.ExportFunction("processBar", (func(entity *common.Entity) *common.Entity)(nil)))

	assert.NoError(t, m.Init(log.With()))
	//m.SetDebugLevel(wasm.DebugLevelMem)

	arg := common.BuildEntity(
		&common.EntityProperty{
			Key: wasm.BuildString("id"),
			Value: &common.Value{
				Kind:  common.ValueKindBytes,
				Value: wasm.MustBuildByteArrayFromHex("0x010203040506"),
			},
		},
		&common.EntityProperty{
			Key: wasm.BuildString("_from"),
			Value: &common.Value{
				Kind:  common.ValueKindBytes,
				Value: wasm.MustBuildByteArrayFromHex("0x01020304050607"),
			},
		},
		&common.EntityProperty{
			Key: wasm.BuildString("_to"),
			Value: &common.Value{
				Kind:  common.ValueKindBytes,
				Value: wasm.MustBuildByteArrayFromHex("0x0102030405060708"),
			},
		},
	)
	//fmt.Println("arg:", arg.Text(", "))
	ret, _, err := m.CallExportFunction(wasm.NewCallContext[CtxData](context.Background()), wasm.CallParams[CtxData]{
		ExportFuncName: "processBar",
		Logger:         log.With(),
	}, arg)
	assert.NoError(t, err)

	entity := ret.(*common.Entity)
	//fmt.Println("ret:", entity.Text(", "))
	assert.Equal(t, common.BuildEntity(
		&common.EntityProperty{
			Key: wasm.BuildString("id"),
			Value: &common.Value{
				Kind:  common.ValueKindBytes,
				Value: wasm.MustBuildByteArrayFromHex("0x010203040506"),
			},
		},
		&common.EntityProperty{
			Key: wasm.BuildString("_from"),
			Value: &common.Value{
				Kind:  common.ValueKindBytes,
				Value: wasm.MustBuildByteArrayFromHex("0x01020304050607"),
			},
		},
		&common.EntityProperty{
			Key: wasm.BuildString("_to"),
			Value: &common.Value{
				Kind:  common.ValueKindBytes,
				Value: wasm.MustBuildByteArrayFromHex("0x0102030405060708"),
			},
		},
		&common.EntityProperty{
			Key: wasm.BuildString("_value"),
			Value: &common.Value{
				Kind:  common.ValueKindBigInt,
				Value: common.MustBuildBigInt(321),
			},
		},
	), entity)
}

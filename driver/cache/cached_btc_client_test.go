package cache

import (
	"github.com/stretchr/testify/assert"
	"golang.org/x/net/context"
	"sentioxyz/sentio/chain"
	"sentioxyz/sentio/processor/protos"
	protos2 "sentioxyz/sentio/service/common/protos"
	"testing"
)

func NewTestCachedBTCClient() (*CachedBTCClient, error) {
	config := chain.NewCustomizedChainConfig("btc-mainnet", "http://localhost:18332")
	return NewCachedBTCClient(config)
}

func TestCachedBTCClient_GetLatestBlock(t *testing.T) {
	t.SkipNow()
	cli, err := NewTestCachedBTCClient()
	assert.NoError(t, err)
	ctx := context.TODO()
	r, err := cli.GetLatestBlock(ctx)
	assert.NoError(t, err)
	assert.True(t, r > 0)
}

func TestCachedBTCClient_SearchTransaction(t *testing.T) {
	t.<PERSON><PERSON><PERSON>()
	cli, err := NewTestCachedBTCClient()
	assert.NoError(t, err)
	ctx := context.TODO()

	prefix := "OP_RETURN 62626e31"
	length := int32(152)
	filter := &protos.BTCTransactionFilter{
		Filter: []*protos.BTCTransactionFilter_Filter{
			{
				Conditions: map[string]*protos.BTCTransactionFilter_Condition{
					"block_number": {
						Gte: &protos2.RichValue{
							Value: &protos2.RichValue_IntValue{
								IntValue: 857589,
							},
						},
					},
				},
			},
		},
		OutputFilter: &protos.BTCTransactionFilter_VOutFilter{
			Filters: &protos.BTCTransactionFilter_Filters{
				Filters: []*protos.BTCTransactionFilter_Filter{
					{
						Conditions: map[string]*protos.BTCTransactionFilter_Condition{
							"script_asm": {
								Prefix:   &prefix,
								LengthEq: &length,
							},
						},
					},
				},
			},
		},
	}

	r, err := cli.GetTransactionByFilter(ctx, filter, 857589, 858589, 10, 0)
	assert.NoError(t, err)
	t.Logf("block: %v", r)
}

import useApi from 'lib/data/use-api'
import { SolidityService, GetMEVInfoRequest } from 'gen/service/solidity/protos/service.pb'
import { useContext } from 'react'
import { ChainIdentifierContext, TxIdentifierContext } from 'lib/debug/context'

export const useMev = (hash?: string, chainId?: string | number) => {
  const identifierName = useContext(TxIdentifierContext)
  const chainIdentifier = useContext(ChainIdentifierContext)
  const req: GetMEVInfoRequest | null =
    chainId?.toString() === '1' && identifierName === 'txId.txHash' && chainIdentifier === 'chainSpec.chainId'
      ? {
          chainSpec: {
            chainId: chainId?.toString()
          },
          txHash: hash
        }
      : null
  return useApi(SolidityService.GetMEVInfo, req, undefined, {
    revalidateIfStale: false,
    revalidateOnFocus: false,
    revalidateOnReconnect: false
  })
}

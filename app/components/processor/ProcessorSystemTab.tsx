import { SystemStatType } from '@sentio/service/processor/protos/service.pb'
import { ProcessorSystemStat } from './ProcessorSystemStat'
import TimeRangePicker from 'components/timerange/TimeRangePicker'
import { now, ago, toDayjs, DateTimeValue } from 'lib/time'
import { useProcessorSystemState } from 'lib/data/use-processor'
import { useMemo, memo, useState, useEffect } from 'react'
import { connect, disconnect } from 'echarts/core'
import { ArrowTopRightOnSquareIcon, InformationCircleIcon } from '@heroicons/react/20/solid'
import { PopoverTooltip } from 'components/common/tooltip/DivTooltip'
import { DisclosurePanel } from 'components/common/panel/DisclosurePanel'
import { ProcessorProfile } from './ProcessorProfile'

export const ProcessorSystemTab = memo(function ProcessorSystemTab({
  id,
  isSubgraph,
  sdkVersion
}: {
  id?: string
  isSubgraph?: boolean
  sdkVersion?: string
}) {
  const [start, setStart] = useState<DateTimeValue | undefined>(ago(3, 'hours'))
  const [end, setEnd] = useState<DateTimeValue | undefined>(now)
  const [startTime, setStartTime] = useState<string | undefined>()
  const [endTime, setEndTime] = useState<string | undefined>()
  useEffect(() => {
    setStartTime(start ? toDayjs(start).toISOString() : undefined)
    setEndTime(end ? toDayjs(end).toISOString() : undefined)
  }, [start, end])
  const { data: cpuData, loading: cpuDataLoading } = useProcessorSystemState(SystemStatType.CPU, id, startTime, endTime)
  const { data: memData, loading: memDataLoading } = useProcessorSystemState(SystemStatType.MEM, id, startTime, endTime)

  const processorCpuData = useMemo(() => {
    if (!cpuData) {
      return
    }
    const { results } = cpuData
    return results?.find((r) => r.metric?.container === 'processor')?.values
  }, [cpuData])
  const processorMemData = useMemo(() => {
    if (!memData) {
      return
    }
    const { results } = memData
    return results?.find((r) => r.metric?.container === 'processor')?.values
  }, [memData])

  const driverCpuData = useMemo(() => {
    if (!cpuData) {
      return
    }
    const { results } = cpuData
    return results?.find((r) => r.metric?.container === 'driver')?.values
  }, [cpuData])
  const driverMemData = useMemo(() => {
    if (!memData) {
      return
    }
    const { results } = memData
    return results?.find((r) => r.metric?.container === 'driver')?.values
  }, [memData])

  useEffect(() => {
    connect('processor')
    connect('driver')
    return () => {
      disconnect('processor')
      disconnect('driver')
    }
  }, [id])

  return (
    <>
      <div className="dark:bg-sentio-gray-100 rounded border bg-white p-4">
        <div className="flex w-full flex-wrap items-center justify-between gap-6">
          <div className="inline-flex items-center gap-2">
            <h1 className="text-base font-bold">System Monitor</h1>
            <a
              title="To view multiple network statuses, visit the service status page."
              href="https://www.sentio.xyz/status/index.html"
              target="_blank"
              rel="noreferrer"
              className="text-gray hover:border-primary-500 hover:text-primary-500 rounded-full border px-2 py-0.5 text-xs"
            >
              Service Status
              <ArrowTopRightOnSquareIcon className="ml-1 inline-block h-3.5 w-3.5 align-text-bottom" />
            </a>
          </div>
          <TimeRangePicker
            startTime={start}
            endTime={end}
            onRefresh={() => {
              setStartTime(start ? toDayjs(start).toISOString() : undefined)
              setEndTime(end ? toDayjs(end).toISOString() : undefined)
            }}
            onChange={(start, end) => {
              setStart(start)
              setEnd(end)
            }}
          />
        </div>
      </div>
      {isSubgraph ? (
        <div className="dark:bg-sentio-gray-100 rounded border bg-white p-4">
          <h1 className="text-ilabel font-bold">Subgraph Status</h1>
          <div className="grid grid-cols-1 gap-4 pt-4 sm:grid-cols-2">
            <ProcessorSystemStat
              type={SystemStatType.CPU}
              data={driverCpuData}
              name="CPU Cores"
              loading={cpuDataLoading}
              group="processor"
            />
            <ProcessorSystemStat
              type={SystemStatType.MEM}
              data={driverMemData}
              name="Memory"
              loading={memDataLoading}
              group="processor"
            />
          </div>
        </div>
      ) : (
        <>
          <div className="dark:bg-sentio-gray-100 rounded border bg-white p-4">
            <div className="flex w-full items-center justify-between">
              <h1 className="text-ilabel font-bold">Processor Status</h1>
              <ProcessorProfile processorId={id} sdkVersion={sdkVersion} />
            </div>
            <div className="grid grid-cols-1 gap-4 pt-4 sm:grid-cols-2">
              <ProcessorSystemStat
                type={SystemStatType.CPU}
                data={processorCpuData}
                name="Processor CPU Cores"
                loading={cpuDataLoading}
                group="processor"
              />
              <ProcessorSystemStat
                type={SystemStatType.MEM}
                data={processorMemData}
                name="Processor Memory"
                loading={memDataLoading}
                group="processor"
              />
            </div>
          </div>
          <div className="dark:bg-sentio-gray-100 rounded border bg-white p-4">
            <DisclosurePanel
              titleClassName="!bg-white dark:!bg-sentio-gray-100"
              className="dark:!bg-sentio-gray-100 !bg-white"
              defaultOpen={isSubgraph}
              title={
                <h1 className="text-ilabel font-bold">
                  Driver Status
                  <span className="ml-2 inline-block align-text-bottom">
                    <PopoverTooltip
                      text={
                        <span className="text-gray text-icontent break-words font-normal">
                          {"The system process that handling processor's data fetching and transforming."}
                        </span>
                      }
                    >
                      <InformationCircleIcon className="text-gray h-4 w-4" />
                    </PopoverTooltip>
                  </span>
                </h1>
              }
            >
              <div className="grid grid-cols-1 gap-4 pt-4 sm:grid-cols-2">
                <ProcessorSystemStat
                  type={SystemStatType.CPU}
                  data={driverCpuData}
                  name="Driver CPU Cores"
                  loading={cpuDataLoading}
                  group="driver"
                />
                <ProcessorSystemStat
                  type={SystemStatType.MEM}
                  data={driverMemData}
                  name="Driver Memory"
                  loading={memDataLoading}
                  group="driver"
                />
              </div>
            </DisclosurePanel>
          </div>
        </>
      )}
    </>
  )
})

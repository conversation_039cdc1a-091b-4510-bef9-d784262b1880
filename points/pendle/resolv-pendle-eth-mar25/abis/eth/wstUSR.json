[{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "AddressInsufficientBalance", "type": "error"}, {"inputs": [], "name": "ECDSAInvalidSignature", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "ECDSAInvalidSignatureLength", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "ECDSAInvalidSignatureS", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "deadline", "type": "uint256"}], "name": "ERC2612ExpiredSignature", "type": "error"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "ERC2612InvalidSigner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "uint256", "name": "_wstUSRAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_maxWstUSRAmount", "type": "uint256"}], "name": "ExceededMaxRedeem", "type": "error"}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "uint256", "name": "_usrAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_maxUsrAmount", "type": "uint256"}], "name": "ExceededMaxWithdraw", "type": "error"}, {"inputs": [], "name": "FailedInnerCall", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "_idempotencyKey", "type": "bytes32"}], "name": "IdempotencyKeyAlreadyExist", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "currentNonce", "type": "uint256"}], "name": "InvalidAccountNonce", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "InvalidAmount", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "MathOverflowedMulDiv", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [], "name": "ZeroAddress", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [], "name": "EIP712DomainChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_receiver", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_stUSRAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_wstUSRAmount", "type": "uint256"}], "name": "Unwrap", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "receiver", "type": "address"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "Withdraw", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_receiver", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_stUSRAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_wstUSRAmount", "type": "uint256"}], "name": "Wrap", "type": "event"}, {"inputs": [], "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ST_USR_SHARES_OFFSET", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "asset", "outputs": [{"internalType": "address", "name": "usr<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_wstUSRAmount", "type": "uint256"}], "name": "convertToAssets", "outputs": [{"internalType": "uint256", "name": "usrAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_usrAmount", "type": "uint256"}], "name": "convertToShares", "outputs": [{"internalType": "uint256", "name": "wstUSRAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_usrAmount", "type": "uint256"}, {"internalType": "address", "name": "_receiver", "type": "address"}], "name": "deposit", "outputs": [{"internalType": "uint256", "name": "wstUSRAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_usrAmount", "type": "uint256"}], "name": "deposit", "outputs": [{"internalType": "uint256", "name": "wstUSRAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_usrAmount", "type": "uint256"}, {"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "uint256", "name": "_deadline", "type": "uint256"}, {"internalType": "uint8", "name": "_v", "type": "uint8"}, {"internalType": "bytes32", "name": "_r", "type": "bytes32"}, {"internalType": "bytes32", "name": "_s", "type": "bytes32"}], "name": "depositWithPermit", "outputs": [{"internalType": "uint256", "name": "wstUSRAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "eip712Domain", "outputs": [{"internalType": "bytes1", "name": "fields", "type": "bytes1"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "address", "name": "verifyingContract", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "uint256[]", "name": "extensions", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_name", "type": "string"}, {"internalType": "string", "name": "_symbol", "type": "string"}, {"internalType": "address", "name": "_st<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "maxDeposit", "outputs": [{"internalType": "uint256", "name": "maxUsrAmount", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "maxMint", "outputs": [{"internalType": "uint256", "name": "maxWstUSRAmount", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "max<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "maxWstUSRAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}], "name": "max<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "maxUsrAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_wstUSRAmount", "type": "uint256"}, {"internalType": "address", "name": "_receiver", "type": "address"}], "name": "mint", "outputs": [{"internalType": "uint256", "name": "usrAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_wstUSRAmount", "type": "uint256"}], "name": "mint", "outputs": [{"internalType": "uint256", "name": "usrAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_wstUSRAmount", "type": "uint256"}, {"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "uint256", "name": "_deadline", "type": "uint256"}, {"internalType": "uint8", "name": "_v", "type": "uint8"}, {"internalType": "bytes32", "name": "_r", "type": "bytes32"}, {"internalType": "bytes32", "name": "_s", "type": "bytes32"}], "name": "mintWithPermit", "outputs": [{"internalType": "uint256", "name": "usrAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "permit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_usrAmount", "type": "uint256"}], "name": "previewDeposit", "outputs": [{"internalType": "uint256", "name": "wstUSRAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_wstUSRAmount", "type": "uint256"}], "name": "previewMint", "outputs": [{"internalType": "uint256", "name": "usrAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_wstUSRAmount", "type": "uint256"}], "name": "previewRedeem", "outputs": [{"internalType": "uint256", "name": "usrAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_usrAmount", "type": "uint256"}], "name": "previewWithdraw", "outputs": [{"internalType": "uint256", "name": "wstUSRAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_wstUSRAmount", "type": "uint256"}, {"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "address", "name": "_owner", "type": "address"}], "name": "redeem", "outputs": [{"internalType": "uint256", "name": "usrAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_wstUSRAmount", "type": "uint256"}], "name": "redeem", "outputs": [{"internalType": "uint256", "name": "usrAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "st<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalAssets", "outputs": [{"internalType": "uint256", "name": "totalManagedUsrAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_wstUSRAmount", "type": "uint256"}, {"internalType": "address", "name": "_receiver", "type": "address"}], "name": "unwrap", "outputs": [{"internalType": "uint256", "name": "stUSRAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_wstUSRAmount", "type": "uint256"}], "name": "unwrap", "outputs": [{"internalType": "uint256", "name": "stUSRAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_usrAmount", "type": "uint256"}], "name": "withdraw", "outputs": [{"internalType": "uint256", "name": "wstUSRAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_usrAmount", "type": "uint256"}, {"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "address", "name": "_owner", "type": "address"}], "name": "withdraw", "outputs": [{"internalType": "uint256", "name": "wstUSRAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_stUSRAmount", "type": "uint256"}, {"internalType": "address", "name": "_receiver", "type": "address"}], "name": "wrap", "outputs": [{"internalType": "uint256", "name": "wstUSRAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_stUSRAmount", "type": "uint256"}], "name": "wrap", "outputs": [{"internalType": "uint256", "name": "wstUSRAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_stUSRAmount", "type": "uint256"}, {"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "uint256", "name": "_deadline", "type": "uint256"}, {"internalType": "uint8", "name": "_v", "type": "uint8"}, {"internalType": "bytes32", "name": "_r", "type": "bytes32"}, {"internalType": "bytes32", "name": "_s", "type": "bytes32"}], "name": "wrapWithPermit", "outputs": [{"internalType": "uint256", "name": "wstUSRAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}]
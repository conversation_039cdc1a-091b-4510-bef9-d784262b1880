import { Eth<PERSON><PERSON>nId, getProvider } from "@sentio/sdk/eth";
import { getHypervisorContract } from "./types/eth/hypervisor.js";

export const TOKEN_DECIMALS = 18;

export interface Config {
  network: EthChainId;
  addresses: string[];
  lynexGauges?: {
    [key: string]: {
      address: string;
      startBlock: number;
    };
  };
  stone: string;
}

export const configs: Config[] = [
  {
    network: EthChainId.MANTA_PACIFIC,
    addresses: [
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
    ],
    stone: "******************************************",
  },
  {
    network: EthChainId.LINEA,
    addresses: ["******************************************"],
    lynexGauges: {
      "******************************************": {
        address: "******************************************",
        startBlock: await getCreationBlock(
          EthChainId.LINEA,
          "******************************************"
        ),
      },
    },
    stone: "******************************************",
  },
];

export interface BeefyConfig {
  network: EthChainId;
  vault: string;
  strategy: string;
  gammaVault: string;
}

// user -> vault aSTONE-WETH, vault -> user: vault lp token
// strategy -> lynex gauge: aSTONE-WETH, lynex gauge -> strategy: gauge lp token
export const beefyConfigs: BeefyConfig[] = [
  {
    network: EthChainId.LINEA,
    strategy: "******************************************",
    vault: "******************************************",
    gammaVault: "******************************************",
  },
];

export const token0IsStone: Record<string, boolean> = {};

for (const conf of configs) {
  for (const address of conf.addresses) {
    const c = getHypervisorContract(conf.network, address);
    const token0 = await c.token0();
    token0IsStone[address.toLowerCase()] =
      token0.toLowerCase() == conf.stone.toLowerCase();
  }
}

async function getCreationBlock(
  network: EthChainId,
  address: string
): Promise<number> {
  const provider = getProvider(network);
  let l = 0;
  let r = await provider.getBlockNumber();
  while (l < r) {
    const m = Math.floor((l + r) / 2);
    const code = await provider.getCode(address, m);
    if (code.length > 2) {
      r = m;
    } else {
      l = m + 1;
    }
  }
  return l;
}

import { DebuggerClient } from '../debugger-client'
import request from 'sync-request'
import { InitArguments } from '../types'
import { safeNativize } from '@sentio/debugger-common'
import assert from 'assert'
import { describe, it, before, afterEach } from 'node:test'

describe('Truffle debugger', () => {
  let client: DebuggerClient
  before(async () => {
    client = new DebuggerClient()
    const callTraceResp = request(
      'GET',
      'https://test.sentio.xyz/api/v1/solidity/call_trace?txHash=0x857c1331d430048a16b71515464c6d68e7cc17337de82afe0151652435862b4a&withInternalCalls=true',
      { json: true }
    )
    const callTrace = JSON.parse(callTraceResp.body as string)
    const resp = request(
      'GET',
      'https://test.sentio.xyz/api/v1/solidity/fetch_and_compile?txHash=0x857c1331d430048a16b71515464c6d68e7cc17337de82afe0151652435862b4a',
      { json: true }
    )
    const body = JSON.parse(resp.body as string)
    const shimmedCompilations = []
    for (const key in body.result) {
      const value = body.result[key]
      // @ts-ignore
      shimmedCompilations.push(value)
    }
    const init_args: InitArguments = {
      providerURL: 'https://test.sentio.xyz/api/v1/solidity',
      txId: {
        txHash: '0x857c1331d430048a16b71515464c6d68e7cc17337de82afe0151652435862b4a'
      },
      shimmedCompilations: shimmedCompilations,
      callTrace,
      chainId: '1',
      storageLookup: false,
      disableOptimizer: false,
      ignoreGasCost: false,
      processedAST: undefined,
      enableTraceStreaming: true
    }
    await client.initialize(init_args)
  }),
    afterEach(async () => {
      await client.reset()
    }),
    it('advance multiple steps', async () => {
      await client.advance(100000)
      assert.ok(client.finished)
    }),
    it('breakpoint and evaluate variables', async () => {
      await client.addBreakpoint({
        compilationId: 'externalFor(******************************************)Via(etherscan)Number(0)',
        sourcePath: 'FiatTokenProxy.sol',
        line: 21
      })

      assert.equal((await client.continueUntilBreakpoint()).lines?.start.line, 21)
      assert.equal(await client.evaluateExpression('now + 2'), 1670734885)
      assert.equal(await client.evaluateExpression('now + 1'), 1670734884)
    }),
    it('require stacktrace and varaibles', async () => {
      await client.stepNext()
      await client.stepInto()
      await client.stepInto()
      await client.stepInto()
      await client.stepInto()
      await client.stepInto()
      await client.stepInto()
      await client.stepInto()
      await client.stepInto()
      await client.stepInto()
      assert.ok(await client.stepOver())
      assert.ok(await client.requireStacktrace())
      assert.ok(await client.requireVariables())
    }),
    it('Evaluate illegal expression', async () => {
      await client.evaluateExpression('@1').catch((e) => {
        assert.ok(e)
      })
    })

  it('Get code', async () => {
    const private_client = new DebuggerClient()
    const callTraceResp = request(
      'GET',
      'https://test.sentio.xyz/api/v1/solidity/call_trace?txHash=0x857c1331d430048a16b71515464c6d68e7cc17337de82afe0151652435862b4a&withInternalCalls=true',
      { json: true }
    )
    const callTrace = JSON.parse(callTraceResp.body as string)
    const resp = request(
      'GET',
      'https://test.sentio.xyz/api/v1/solidity/fetch_and_compile?txHash=0x9da0c2284049126f53921841c38604c074f3f224e71172a66d6d283db3ae09f6',
      { json: true }
    )
    const body = JSON.parse(resp.body as string)
    const shimmedCompilations = []
    for (const key in body.result) {
      const value = body.result[key]
      // @ts-ignore
      shimmedCompilations.push(value)
    }
    const init_args: InitArguments = {
      providerURL: 'https://test.sentio.xyz/api/v1/solidity',
      txId: {
        txHash: '0x9da0c2284049126f53921841c38604c074f3f224e71172a66d6d283db3ae09f6'
      },
      shimmedCompilations: shimmedCompilations,
      callTrace,
      chainId: '1',
      storageLookup: false,
      disableOptimizer: false,
      ignoreGasCost: false,
      processedAST: undefined,
      enableTraceStreaming: true
    }
    await private_client.initialize(init_args)
    await private_client.stepInto()
    await private_client.stepInto()
    await private_client.stepInto()
    await private_client.stepInto()
    await private_client.stepInto()
    assert.ok(await private_client.requireVariables())
  }),
    it('test safeNative', () => {
      // @ts-ignore
      const res = safeNativize({
        type: {
          typeClass: 'uint',
          bits: 256
        },
        kind: 'value',
        value: {
          // @ts-ignore
          asBN: {
            negative: 0,
            words: [58647702, 219],
            length: 2,
            red: null
          } as any,
          rawAsBN: {
            negative: 0,
            words: [58647702, 219],
            length: 2,
            red: null
          } as any
        }
      })
      assert.equal(res, 14755488918)
    })
})
